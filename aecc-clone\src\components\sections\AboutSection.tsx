import React from 'react';
import { CheckCir<PERSON>, Users, Globe, Award } from 'lucide-react';

const AboutSection = () => {
  const stats = [
    {
      icon: Users,
      number: '760,000+',
      label: 'Students Assisted',
      description: 'Successfully guided students to their dream destinations'
    },
    {
      icon: Globe,
      number: '15+',
      label: 'Years of Experience',
      description: 'Serving students since 2008 with excellence'
    },
    {
      icon: Award,
      number: '100+',
      label: 'Partner Universities',
      description: 'Strong partnerships with top institutions worldwide'
    }
  ];

  const features = [
    'Free consultation and guidance',
    'Expert visa application support',
    'Scholarship assistance',
    'Test preparation programs',
    'Accommodation support',
    'Post-arrival services'
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div>
            <div className="mb-8">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Bridging the gap between students and their study abroad dreams since 2008
              </h2>
              <p className="text-lg text-gray-600 leading-relaxed mb-6">
                At AECC, we take pride in offering an unparalleled consultation experience. We strive to become the best education consultancy in Nepal. To do so, our consultants ensure a seamless process and earnestly commit to meeting your study abroad needs from start to finish.
              </p>
              <p className="text-gray-600 leading-relaxed mb-8">
                Our dedicated team of experienced counselors provides personalized guidance to help you choose the right course, university, and destination that aligns with your career goals and aspirations.
              </p>
            </div>

            {/* Features List */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                  <span className="text-gray-700">{feature}</span>
                </div>
              ))}
            </div>

            {/* CTA Button */}
            <button className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors shadow-md">
              Talk to a counsellor
            </button>
          </div>

          {/* Right Content - Stats */}
          <div className="relative">
            {/* Background decoration */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-3xl transform rotate-3"></div>
            
            <div className="relative bg-white rounded-3xl p-8 shadow-xl">
              <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">
                Our Impact in Numbers
              </h3>
              
              <div className="space-y-8">
                {stats.map((stat, index) => {
                  const IconComponent = stat.icon;
                  return (
                    <div key={index} className="flex items-start space-x-4">
                      <div className="bg-blue-100 rounded-2xl p-3 flex-shrink-0">
                        <IconComponent className="w-8 h-8 text-blue-600" />
                      </div>
                      <div>
                        <div className="text-3xl font-bold text-gray-900 mb-1">
                          {stat.number}
                        </div>
                        <div className="text-lg font-semibold text-gray-800 mb-1">
                          {stat.label}
                        </div>
                        <div className="text-gray-600 text-sm">
                          {stat.description}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Achievement badge */}
              <div className="mt-8 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl text-center">
                <div className="text-sm font-semibold text-blue-600 mb-1">
                  🏆 Award Winning
                </div>
                <div className="text-xs text-gray-600">
                  Recognized as Nepal's leading education consultancy
                </div>
              </div>
            </div>

            {/* Floating elements */}
            <div className="absolute -top-4 -right-4 w-20 h-20 bg-yellow-200 rounded-full mix-blend-multiply filter blur-xl opacity-70"></div>
            <div className="absolute -bottom-4 -left-4 w-20 h-20 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-70"></div>
          </div>
        </div>

        {/* Bottom section */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-3xl p-8 max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Why Choose AECC Nepal?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              We are committed to providing honest, transparent, and personalized guidance to help you achieve your study abroad dreams. Our success is measured by your success.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors shadow-md">
                Start Your Journey
              </button>
              <button className="border border-gray-300 text-gray-700 px-8 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors">
                Learn More About Us
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
