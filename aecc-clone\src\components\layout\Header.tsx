"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { ChevronDown, Menu } from "lucide-react";

const Header = () => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <header className="bg-white shadow-sm border-b sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center">
            <Image
              src="/images/new-logo.svg"
              alt="AECC Nepal"
              width={120}
              height={40}
              className="h-10 w-auto"
            />
          </Link>

          {/* Navigation - Desktop */}
          <nav className="hidden lg:flex items-center space-x-6">
            {/* Student Services Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger className="flex items-center text-gray-700 hover:text-blue-600 transition-colors">
                Student Services
                <ChevronDown className="ml-1 h-4 w-4" />
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem>
                  <Link href="/student-services/meet-us-online">Meet Us Online</Link>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Link href="/student-services/admission-counselling">Admission Counselling</Link>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Link href="/student-services/student-health-insurance">Student Health Insurance</Link>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Link href="/student-services/student-accommodation">Student Accommodation</Link>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Link href="/student-services/student-visa-service">Student Visa Service</Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Study Abroad Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger className="flex items-center text-gray-700 hover:text-blue-600 transition-colors">
                Study Abroad
                <ChevronDown className="ml-1 h-4 w-4" />
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem>
                  <Link href="/study-abroad/study-in-australia">Study in Australia</Link>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Link href="/study-abroad/study-in-canada">Study in Canada</Link>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Link href="/study-abroad/study-in-uk">Study in UK</Link>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Link href="/study-abroad/study-in-usa">Study in USA</Link>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Link href="/study-abroad/study-in-new-zealand">Study in New Zealand</Link>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Link href="/study-abroad/study-in-europe">Study in Europe</Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Scholarships Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger className="flex items-center text-gray-700 hover:text-blue-600 transition-colors">
                Scholarships
                <ChevronDown className="ml-1 h-4 w-4" />
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem>
                  <Link href="/scholarships/uk">Scholarships in UK</Link>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Link href="/scholarships/australia">Scholarships in Australia</Link>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Link href="/scholarships/canada">Scholarships in Canada</Link>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Link href="/scholarships/usa">Scholarships in USA</Link>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Link href="/scholarships/new-zealand">Scholarships in New Zealand</Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <Link href="/english-proficiency-coaching" className="text-gray-700 hover:text-blue-600 transition-colors">
              Test Preparation
            </Link>
            <Link href="/student-testimonials" className="text-gray-700 hover:text-blue-600 transition-colors">
              Success Stories
            </Link>
            <Link href="/upcoming-event" className="text-gray-700 hover:text-blue-600 transition-colors">
              Upcoming Events
            </Link>
            <Link href="/blog" className="text-gray-700 hover:text-blue-600 transition-colors">
              Blog
            </Link>
          </nav>

          {/* CTA Button */}
          <div className="hidden lg:block">
            <button className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors font-medium">
              Meet us Online
            </button>
          </div>

          {/* Mobile menu */}
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
              <button className="lg:hidden p-2">
                <Menu className="h-6 w-6" />
              </button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[300px] sm:w-[400px]">
              <nav className="flex flex-col space-y-4 mt-8">
                <Link href="/student-services" className="text-gray-700 hover:text-blue-600 py-2">
                  Student Services
                </Link>
                <Link href="/study-abroad" className="text-gray-700 hover:text-blue-600 py-2">
                  Study Abroad
                </Link>
                <Link href="/scholarships" className="text-gray-700 hover:text-blue-600 py-2">
                  Scholarships
                </Link>
                <Link href="/english-proficiency-coaching" className="text-gray-700 hover:text-blue-600 py-2">
                  Test Preparation
                </Link>
                <Link href="/student-testimonials" className="text-gray-700 hover:text-blue-600 py-2">
                  Success Stories
                </Link>
                <Link href="/upcoming-event" className="text-gray-700 hover:text-blue-600 py-2">
                  Upcoming Events
                </Link>
                <Link href="/blog" className="text-gray-700 hover:text-blue-600 py-2">
                  Blog
                </Link>
                <button className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition-colors font-medium mt-4">
                  Meet us Online
                </button>
              </nav>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
};

export default Header;
