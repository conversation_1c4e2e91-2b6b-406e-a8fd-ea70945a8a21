"use client";

import React, { useState } from 'react';
import { Star, Quote, ChevronLeft, ChevronRight } from 'lucide-react';

const TestimonialsSection = () => {
  const testimonials = [
    {
      name: '<PERSON><PERSON>',
      destination: 'Canada',
      image: '/images/student-1.jpg',
      rating: 5,
      text: 'I was looking for a top education consultant in Kathmandu and luckily a friend recommended me to the best education consultancy in Nepal - AECC Nepal. The consultancy in Nepal for Canada assisted me from getting a letter of offer for my education, acquiring a student insurance coverage for my stay, and finally arranging my visa.',
      course: 'Business Administration',
      university: 'University of Toronto'
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      destination: 'Australia',
      image: '/images/student-2.jpg',
      rating: 5,
      text: 'AECC Nepal is the best overseas education consultant in Nepal. They provided great assistance with my student visa application in Melbourne, Australia. Additionally, the team has been very approachable and always there to answer all my queries. They are the best study abroad consultancy in Nepal for Australia.',
      course: 'Computer Science',
      university: 'University of Melbourne'
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      destination: 'Canada',
      image: '/images/student-3.jpg',
      rating: 5,
      text: 'I am ever grateful to the competent team for helping me attain my Canada student visa in no time. The consultation and customer services they provide are efficient and excellent. AECC Nepal is the best education consultancy in Kathmandu and also they are the best Overseas education consultancy in Nepal for Canada.',
      course: 'Engineering',
      university: 'University of British Columbia'
    },
    {
      name: 'Priya Sharma',
      destination: 'UK',
      image: '/images/student-4.jpg',
      rating: 5,
      text: 'The team at AECC helped me secure admission to my dream university in the UK. Their guidance throughout the application process was invaluable, and they made sure I was well-prepared for my journey abroad.',
      course: 'International Relations',
      university: 'London School of Economics'
    }
  ];

  const [currentIndex, setCurrentIndex] = useState(0);

  const nextTestimonial = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prevIndex) => (prevIndex - 1 + testimonials.length) % testimonials.length);
  };

  const currentTestimonial = testimonials[currentIndex];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Top education consultancy in Nepal, truly!
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            We are thrilled to have been a part of 760,000 successful student stories. Hear a few of their experiences
          </p>
        </div>

        {/* Main Testimonial Display */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-3xl p-8 md:p-12 relative overflow-hidden">
            {/* Quote Icon */}
            <div className="absolute top-6 left-6 text-blue-200">
              <Quote className="w-12 h-12" />
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
              {/* Student Info */}
              <div className="text-center lg:text-left">
                {/* Student Avatar */}
                <div className="w-24 h-24 mx-auto lg:mx-0 mb-4 bg-gradient-to-br from-blue-400 to-indigo-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-2xl font-bold">
                    {currentTestimonial.name.charAt(0)}
                  </span>
                </div>

                <h3 className="text-xl font-bold text-gray-900 mb-1">
                  {currentTestimonial.name}
                </h3>
                <p className="text-blue-600 font-semibold mb-2">
                  {currentTestimonial.destination}
                </p>
                <p className="text-sm text-gray-600 mb-1">
                  {currentTestimonial.course}
                </p>
                <p className="text-sm text-gray-500">
                  {currentTestimonial.university}
                </p>

                {/* Rating */}
                <div className="flex justify-center lg:justify-start mt-4">
                  {[...Array(currentTestimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>
              </div>

              {/* Testimonial Text */}
              <div className="lg:col-span-2">
                <blockquote className="text-gray-700 text-lg leading-relaxed italic">
                  "{currentTestimonial.text}"
                </blockquote>
              </div>
            </div>

            {/* Navigation */}
            <div className="flex justify-between items-center mt-8">
              <button
                onClick={prevTestimonial}
                className="p-2 rounded-full bg-white shadow-md hover:shadow-lg transition-shadow"
              >
                <ChevronLeft className="w-6 h-6 text-gray-600" />
              </button>

              {/* Dots Indicator */}
              <div className="flex space-x-2">
                {testimonials.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentIndex(index)}
                    className={`w-3 h-3 rounded-full transition-colors ${
                      index === currentIndex ? 'bg-blue-600' : 'bg-gray-300'
                    }`}
                  />
                ))}
              </div>

              <button
                onClick={nextTestimonial}
                className="p-2 rounded-full bg-white shadow-md hover:shadow-lg transition-shadow"
              >
                <ChevronRight className="w-6 h-6 text-gray-600" />
              </button>
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div className="text-center">
            <div className="text-4xl font-bold text-blue-600 mb-2">760,000+</div>
            <div className="text-gray-600">Students Assisted</div>
          </div>
          <div className="text-center">
            <div className="text-4xl font-bold text-green-600 mb-2">98%</div>
            <div className="text-gray-600">Visa Success Rate</div>
          </div>
          <div className="text-center">
            <div className="text-4xl font-bold text-purple-600 mb-2">15+</div>
            <div className="text-gray-600">Years of Excellence</div>
          </div>
        </div>

        {/* CTA */}
        <div className="text-center mt-12">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Ready to write your success story?
          </h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Join thousands of students who have achieved their study abroad dreams with AECC's expert guidance.
          </p>
          <button className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors shadow-md">
            Start Your Journey Today
          </button>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
