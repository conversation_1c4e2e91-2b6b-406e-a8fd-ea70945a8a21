import React from 'react';
import Link from 'next/link';
import { 
  BookOpen, 
  GraduationCap, 
  FileText, 
  Users, 
  School, 
  Home, 
  Shield 
} from 'lucide-react';

const ServicesSection = () => {
  const services = [
    {
      icon: BookOpen,
      title: 'Test Preparation',
      description: 'IELTS, TOEFL, PTE, GRE, SAT preparation programs',
      href: '/english-proficiency-coaching',
      color: 'bg-blue-500'
    },
    {
      icon: GraduationCap,
      title: 'Scholarship Guidance',
      description: 'Find and apply for scholarships worldwide',
      href: '/scholarships',
      color: 'bg-green-500'
    },
    {
      icon: FileText,
      title: 'Student Visa Service',
      description: 'Complete visa application assistance',
      href: '/student-services/student-visa-service',
      color: 'bg-purple-500'
    },
    {
      icon: Users,
      title: 'Education Counseling',
      description: 'Personalized admission counselling',
      href: '/student-services/admission-counselling',
      color: 'bg-orange-500'
    },
    {
      icon: School,
      title: 'University Admission',
      description: 'Application support for top universities',
      href: '/study-abroad',
      color: 'bg-red-500'
    },
    {
      icon: Home,
      title: 'Student Accommodation',
      description: 'Find the perfect place to stay',
      href: '/student-services/student-accommodation',
      color: 'bg-indigo-500'
    },
    {
      icon: Shield,
      title: 'Student Health Insurance',
      description: 'Comprehensive health coverage',
      href: '/student-services/student-health-insurance',
      color: 'bg-teal-500'
    }
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            With you at every step of your<br />
            study abroad journey
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-8">
            Get personalised, friendly, honest guidance for free<br />
            from the top consultancy in Nepal
          </p>
          <button className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors shadow-md">
            Book a free consultation
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mt-16">
          {services.map((service, index) => {
            const IconComponent = service.icon;
            return (
              <Link
                key={service.title}
                href={service.href}
                className="group relative bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100"
              >
                {/* Icon */}
                <div className={`${service.color} w-16 h-16 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform`}>
                  <IconComponent className="w-8 h-8 text-white" />
                </div>

                {/* Content */}
                <h3 className="text-lg font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                  {service.title}
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {service.description}
                </p>

                {/* Arrow indicator */}
                <div className="absolute top-6 right-6 opacity-0 group-hover:opacity-100 transition-opacity">
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>

                {/* Decorative gradient */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity"></div>
              </Link>
            );
          })}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Ready to start your study abroad journey?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Join thousands of students who have successfully achieved their dreams of studying abroad with AECC's expert guidance.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors shadow-md">
                Get Started Today
              </button>
              <button className="border border-blue-600 text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors">
                Download Brochure
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
