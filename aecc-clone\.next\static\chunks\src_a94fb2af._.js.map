{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/consultancy/aecc-clone/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/consultancy/aecc-clone/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,KAEoC;QAFpC,EACpB,GAAG,OACqD,GAFpC;IAGpB,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,KAEgC;QAFhC,EAC1B,GAAG,OACuD,GAFhC;IAG1B,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,KAEgC;QAFhC,EAC3B,GAAG,OACwD,GAFhC;IAG3B,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,KAIgC;QAJhC,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD,GAJhC;IAK3B,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,KAEgC;QAFhC,EACzB,GAAG,OACsD,GAFhC;IAGzB,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,KAQzB;QARyB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ,GARyB;IASxB,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,KAKgC;QALhC,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D,GALhC;IAMhC,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,KAEgC;QAFhC,EAC9B,GAAG,OAC2D,GAFhC;IAG9B,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,KAIgC;QAJhC,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D,GAJhC;IAK7B,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,KAM1B;QAN0B,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ,GAN0B;IAOzB,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,KAGgC;QAHhC,EAC7B,SAAS,EACT,GAAG,OAC0D,GAHhC;IAI7B,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,KAGC;QAHD,EAC5B,SAAS,EACT,GAAG,OAC0B,GAHD;IAI5B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,KAEgC;QAFhC,EACvB,GAAG,OACoD,GAFhC;IAGvB,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,KAO/B;QAP+B,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ,GAP+B;IAQ9B,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/consultancy/aecc-clone/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,KAA8D;QAA9D,EAAE,GAAG,OAAyD,GAA9D;IACb,qBAAO,6LAAC,qKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;KAFS;AAIT,SAAS,aAAa,KAEgC;QAFhC,EACpB,GAAG,OACiD,GAFhC;IAGpB,qBAAO,6LAAC,qKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,WAAW,KAEgC;QAFhC,EAClB,GAAG,OAC+C,GAFhC;IAGlB,qBAAO,6LAAC,qKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,aAAa,KAGgC;QAHhC,EACpB,SAAS,EACT,GAAG,OACiD,GAHhC;IAIpB,qBACE,6LAAC,qKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,KAOrB;QAPqB,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ,GAPqB;IAQpB,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,6LAAC,mMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAnCS;AAqCT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAGgC;QAHhC,EAClB,SAAS,EACT,GAAG,OAC+C,GAHhC;IAIlB,qBACE,6LAAC,qKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,KAGgC;QAHhC,EACxB,SAAS,EACT,GAAG,OACqD,GAHhC;IAIxB,qBACE,6LAAC,qKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 546, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/consultancy/aecc-clone/src/components/layout/Header.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport { Sheet, SheetContent, SheetTrigger } from \"@/components/ui/sheet\";\nimport { ChevronDown, Menu } from \"lucide-react\";\n\nconst Header = () => {\n  const [isOpen, setIsOpen] = useState(false);\n\n  return (\n    <header className=\"bg-white shadow-sm border-b sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center\">\n            <Image\n              src=\"/images/new-logo.svg\"\n              alt=\"AECC Nepal\"\n              width={120}\n              height={40}\n              className=\"h-10 w-auto\"\n            />\n          </Link>\n\n          {/* Navigation - Desktop */}\n          <nav className=\"hidden lg:flex items-center space-x-6\">\n            {/* Student Services Dropdown */}\n            <DropdownMenu>\n              <DropdownMenuTrigger className=\"flex items-center text-gray-700 hover:text-blue-600 transition-colors\">\n                Student Services\n                <ChevronDown className=\"ml-1 h-4 w-4\" />\n              </DropdownMenuTrigger>\n              <DropdownMenuContent>\n                <DropdownMenuItem>\n                  <Link href=\"/student-services/meet-us-online\">Meet Us Online</Link>\n                </DropdownMenuItem>\n                <DropdownMenuItem>\n                  <Link href=\"/student-services/admission-counselling\">Admission Counselling</Link>\n                </DropdownMenuItem>\n                <DropdownMenuItem>\n                  <Link href=\"/student-services/student-health-insurance\">Student Health Insurance</Link>\n                </DropdownMenuItem>\n                <DropdownMenuItem>\n                  <Link href=\"/student-services/student-accommodation\">Student Accommodation</Link>\n                </DropdownMenuItem>\n                <DropdownMenuItem>\n                  <Link href=\"/student-services/student-visa-service\">Student Visa Service</Link>\n                </DropdownMenuItem>\n              </DropdownMenuContent>\n            </DropdownMenu>\n\n            {/* Study Abroad Dropdown */}\n            <DropdownMenu>\n              <DropdownMenuTrigger className=\"flex items-center text-gray-700 hover:text-blue-600 transition-colors\">\n                Study Abroad\n                <ChevronDown className=\"ml-1 h-4 w-4\" />\n              </DropdownMenuTrigger>\n              <DropdownMenuContent>\n                <DropdownMenuItem>\n                  <Link href=\"/study-abroad/study-in-australia\">Study in Australia</Link>\n                </DropdownMenuItem>\n                <DropdownMenuItem>\n                  <Link href=\"/study-abroad/study-in-canada\">Study in Canada</Link>\n                </DropdownMenuItem>\n                <DropdownMenuItem>\n                  <Link href=\"/study-abroad/study-in-uk\">Study in UK</Link>\n                </DropdownMenuItem>\n                <DropdownMenuItem>\n                  <Link href=\"/study-abroad/study-in-usa\">Study in USA</Link>\n                </DropdownMenuItem>\n                <DropdownMenuItem>\n                  <Link href=\"/study-abroad/study-in-new-zealand\">Study in New Zealand</Link>\n                </DropdownMenuItem>\n                <DropdownMenuItem>\n                  <Link href=\"/study-abroad/study-in-europe\">Study in Europe</Link>\n                </DropdownMenuItem>\n              </DropdownMenuContent>\n            </DropdownMenu>\n\n            {/* Scholarships Dropdown */}\n            <DropdownMenu>\n              <DropdownMenuTrigger className=\"flex items-center text-gray-700 hover:text-blue-600 transition-colors\">\n                Scholarships\n                <ChevronDown className=\"ml-1 h-4 w-4\" />\n              </DropdownMenuTrigger>\n              <DropdownMenuContent>\n                <DropdownMenuItem>\n                  <Link href=\"/scholarships/uk\">Scholarships in UK</Link>\n                </DropdownMenuItem>\n                <DropdownMenuItem>\n                  <Link href=\"/scholarships/australia\">Scholarships in Australia</Link>\n                </DropdownMenuItem>\n                <DropdownMenuItem>\n                  <Link href=\"/scholarships/canada\">Scholarships in Canada</Link>\n                </DropdownMenuItem>\n                <DropdownMenuItem>\n                  <Link href=\"/scholarships/usa\">Scholarships in USA</Link>\n                </DropdownMenuItem>\n                <DropdownMenuItem>\n                  <Link href=\"/scholarships/new-zealand\">Scholarships in New Zealand</Link>\n                </DropdownMenuItem>\n              </DropdownMenuContent>\n            </DropdownMenu>\n\n            <Link href=\"/english-proficiency-coaching\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n              Test Preparation\n            </Link>\n            <Link href=\"/student-testimonials\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n              Success Stories\n            </Link>\n            <Link href=\"/upcoming-event\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n              Upcoming Events\n            </Link>\n            <Link href=\"/blog\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n              Blog\n            </Link>\n          </nav>\n\n          {/* CTA Button */}\n          <div className=\"hidden lg:block\">\n            <button className=\"bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors font-medium\">\n              Meet us Online\n            </button>\n          </div>\n\n          {/* Mobile menu */}\n          <Sheet open={isOpen} onOpenChange={setIsOpen}>\n            <SheetTrigger asChild>\n              <button className=\"lg:hidden p-2\">\n                <Menu className=\"h-6 w-6\" />\n              </button>\n            </SheetTrigger>\n            <SheetContent side=\"right\" className=\"w-[300px] sm:w-[400px]\">\n              <nav className=\"flex flex-col space-y-4 mt-8\">\n                <Link href=\"/student-services\" className=\"text-gray-700 hover:text-blue-600 py-2\">\n                  Student Services\n                </Link>\n                <Link href=\"/study-abroad\" className=\"text-gray-700 hover:text-blue-600 py-2\">\n                  Study Abroad\n                </Link>\n                <Link href=\"/scholarships\" className=\"text-gray-700 hover:text-blue-600 py-2\">\n                  Scholarships\n                </Link>\n                <Link href=\"/english-proficiency-coaching\" className=\"text-gray-700 hover:text-blue-600 py-2\">\n                  Test Preparation\n                </Link>\n                <Link href=\"/student-testimonials\" className=\"text-gray-700 hover:text-blue-600 py-2\">\n                  Success Stories\n                </Link>\n                <Link href=\"/upcoming-event\" className=\"text-gray-700 hover:text-blue-600 py-2\">\n                  Upcoming Events\n                </Link>\n                <Link href=\"/blog\" className=\"text-gray-700 hover:text-blue-600 py-2\">\n                  Blog\n                </Link>\n                <button className=\"bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition-colors font-medium mt-4\">\n                  Meet us Online\n                </button>\n              </nav>\n            </SheetContent>\n          </Sheet>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAMA;AACA;AAAA;;;AAZA;;;;;;;AAcA,MAAM,SAAS;;IACb,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;kCACvB,cAAA,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;;;;;;kCAKd,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,+IAAA,CAAA,eAAY;;kDACX,6LAAC,+IAA<PERSON>,CAAA,sBAAmB;wCAAC,WAAU;;4CAAwE;0DAErG,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;kDAEzB,6LAAC,+IAAA,CAAA,sBAAmB;;0DAClB,6LAAC,+IAAA,CAAA,mBAAgB;0DACf,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAmC;;;;;;;;;;;0DAEhD,6LAAC,+IAAA,CAAA,mBAAgB;0DACf,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAA0C;;;;;;;;;;;0DAEvD,6LAAC,+IAAA,CAAA,mBAAgB;0DACf,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAA6C;;;;;;;;;;;0DAE1D,6LAAC,+IAAA,CAAA,mBAAgB;0DACf,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAA0C;;;;;;;;;;;0DAEvD,6LAAC,+IAAA,CAAA,mBAAgB;0DACf,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAyC;;;;;;;;;;;;;;;;;;;;;;;0CAM1D,6LAAC,+IAAA,CAAA,eAAY;;kDACX,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,WAAU;;4CAAwE;0DAErG,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;kDAEzB,6LAAC,+IAAA,CAAA,sBAAmB;;0DAClB,6LAAC,+IAAA,CAAA,mBAAgB;0DACf,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAmC;;;;;;;;;;;0DAEhD,6LAAC,+IAAA,CAAA,mBAAgB;0DACf,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAgC;;;;;;;;;;;0DAE7C,6LAAC,+IAAA,CAAA,mBAAgB;0DACf,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAA4B;;;;;;;;;;;0DAEzC,6LAAC,+IAAA,CAAA,mBAAgB;0DACf,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAA6B;;;;;;;;;;;0DAE1C,6LAAC,+IAAA,CAAA,mBAAgB;0DACf,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAqC;;;;;;;;;;;0DAElD,6LAAC,+IAAA,CAAA,mBAAgB;0DACf,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;0CAMjD,6LAAC,+IAAA,CAAA,eAAY;;kDACX,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,WAAU;;4CAAwE;0DAErG,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;kDAEzB,6LAAC,+IAAA,CAAA,sBAAmB;;0DAClB,6LAAC,+IAAA,CAAA,mBAAgB;0DACf,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAmB;;;;;;;;;;;0DAEhC,6LAAC,+IAAA,CAAA,mBAAgB;0DACf,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAA0B;;;;;;;;;;;0DAEvC,6LAAC,+IAAA,CAAA,mBAAgB;0DACf,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAuB;;;;;;;;;;;0DAEpC,6LAAC,+IAAA,CAAA,mBAAgB;0DACf,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAoB;;;;;;;;;;;0DAEjC,6LAAC,+IAAA,CAAA,mBAAgB;0DACf,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAA4B;;;;;;;;;;;;;;;;;;;;;;;0CAK7C,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAgC,WAAU;0CAAsD;;;;;;0CAG3G,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAwB,WAAU;0CAAsD;;;;;;0CAGnG,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAkB,WAAU;0CAAsD;;;;;;0CAG7F,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAQ,WAAU;0CAAsD;;;;;;;;;;;;kCAMrF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAO,WAAU;sCAA8F;;;;;;;;;;;kCAMlH,6LAAC,oIAAA,CAAA,QAAK;wBAAC,MAAM;wBAAQ,cAAc;;0CACjC,6LAAC,oIAAA,CAAA,eAAY;gCAAC,OAAO;0CACnB,cAAA,6LAAC;oCAAO,WAAU;8CAChB,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAGpB,6LAAC,oIAAA,CAAA,eAAY;gCAAC,MAAK;gCAAQ,WAAU;0CACnC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAoB,WAAU;sDAAyC;;;;;;sDAGlF,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAgB,WAAU;sDAAyC;;;;;;sDAG9E,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAgB,WAAU;sDAAyC;;;;;;sDAG9E,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAgC,WAAU;sDAAyC;;;;;;sDAG9F,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAwB,WAAU;sDAAyC;;;;;;sDAGtF,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAkB,WAAU;sDAAyC;;;;;;sDAGhF,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAQ,WAAU;sDAAyC;;;;;;sDAGtE,6LAAC;4CAAO,WAAU;sDAAmG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrI;GA/JM;KAAA;uCAiKS", "debugId": null}}, {"offset": {"line": 1128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/consultancy/aecc-clone/src/components/sections/TestimonialsSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from 'react';\nimport { Star, Quote, ChevronLeft, ChevronRight } from 'lucide-react';\n\nconst TestimonialsSection = () => {\n  const testimonials = [\n    {\n      name: '<PERSON><PERSON>',\n      destination: 'Canada',\n      image: '/images/student-1.jpg',\n      rating: 5,\n      text: 'I was looking for a top education consultant in Kathmandu and luckily a friend recommended me to the best education consultancy in Nepal - AECC Nepal. The consultancy in Nepal for Canada assisted me from getting a letter of offer for my education, acquiring a student insurance coverage for my stay, and finally arranging my visa.',\n      course: 'Business Administration',\n      university: 'University of Toronto'\n    },\n    {\n      name: '<PERSON><PERSON><PERSON>',\n      destination: 'Australia',\n      image: '/images/student-2.jpg',\n      rating: 5,\n      text: 'AECC Nepal is the best overseas education consultant in Nepal. They provided great assistance with my student visa application in Melbourne, Australia. Additionally, the team has been very approachable and always there to answer all my queries. They are the best study abroad consultancy in Nepal for Australia.',\n      course: 'Computer Science',\n      university: 'University of Melbourne'\n    },\n    {\n      name: '<PERSON><PERSON><PERSON>',\n      destination: 'Canada',\n      image: '/images/student-3.jpg',\n      rating: 5,\n      text: 'I am ever grateful to the competent team for helping me attain my Canada student visa in no time. The consultation and customer services they provide are efficient and excellent. AECC Nepal is the best education consultancy in Kathmandu and also they are the best Overseas education consultancy in Nepal for Canada.',\n      course: 'Engineering',\n      university: 'University of British Columbia'\n    },\n    {\n      name: 'Priya Sharma',\n      destination: 'UK',\n      image: '/images/student-4.jpg',\n      rating: 5,\n      text: 'The team at AECC helped me secure admission to my dream university in the UK. Their guidance throughout the application process was invaluable, and they made sure I was well-prepared for my journey abroad.',\n      course: 'International Relations',\n      university: 'London School of Economics'\n    }\n  ];\n\n  const [currentIndex, setCurrentIndex] = useState(0);\n\n  const nextTestimonial = () => {\n    setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);\n  };\n\n  const prevTestimonial = () => {\n    setCurrentIndex((prevIndex) => (prevIndex - 1 + testimonials.length) % testimonials.length);\n  };\n\n  const currentTestimonial = testimonials[currentIndex];\n\n  return (\n    <section className=\"py-16 bg-white\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            Top education consultancy in Nepal, truly!\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n            We are thrilled to have been a part of 760,000 successful student stories. Hear a few of their experiences\n          </p>\n        </div>\n\n        {/* Main Testimonial Display */}\n        <div className=\"max-w-4xl mx-auto\">\n          <div className=\"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-3xl p-8 md:p-12 relative overflow-hidden\">\n            {/* Quote Icon */}\n            <div className=\"absolute top-6 left-6 text-blue-200\">\n              <Quote className=\"w-12 h-12\" />\n            </div>\n\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 items-center\">\n              {/* Student Info */}\n              <div className=\"text-center lg:text-left\">\n                {/* Student Avatar */}\n                <div className=\"w-24 h-24 mx-auto lg:mx-0 mb-4 bg-gradient-to-br from-blue-400 to-indigo-600 rounded-full flex items-center justify-center\">\n                  <span className=\"text-white text-2xl font-bold\">\n                    {currentTestimonial.name.charAt(0)}\n                  </span>\n                </div>\n\n                <h3 className=\"text-xl font-bold text-gray-900 mb-1\">\n                  {currentTestimonial.name}\n                </h3>\n                <p className=\"text-blue-600 font-semibold mb-2\">\n                  {currentTestimonial.destination}\n                </p>\n                <p className=\"text-sm text-gray-600 mb-1\">\n                  {currentTestimonial.course}\n                </p>\n                <p className=\"text-sm text-gray-500\">\n                  {currentTestimonial.university}\n                </p>\n\n                {/* Rating */}\n                <div className=\"flex justify-center lg:justify-start mt-4\">\n                  {[...Array(currentTestimonial.rating)].map((_, i) => (\n                    <Star key={i} className=\"w-5 h-5 text-yellow-400 fill-current\" />\n                  ))}\n                </div>\n              </div>\n\n              {/* Testimonial Text */}\n              <div className=\"lg:col-span-2\">\n                <blockquote className=\"text-gray-700 text-lg leading-relaxed italic\">\n                  \"{currentTestimonial.text}\"\n                </blockquote>\n              </div>\n            </div>\n\n            {/* Navigation */}\n            <div className=\"flex justify-between items-center mt-8\">\n              <button\n                onClick={prevTestimonial}\n                className=\"p-2 rounded-full bg-white shadow-md hover:shadow-lg transition-shadow\"\n              >\n                <ChevronLeft className=\"w-6 h-6 text-gray-600\" />\n              </button>\n\n              {/* Dots Indicator */}\n              <div className=\"flex space-x-2\">\n                {testimonials.map((_, index) => (\n                  <button\n                    key={index}\n                    onClick={() => setCurrentIndex(index)}\n                    className={`w-3 h-3 rounded-full transition-colors ${\n                      index === currentIndex ? 'bg-blue-600' : 'bg-gray-300'\n                    }`}\n                  />\n                ))}\n              </div>\n\n              <button\n                onClick={nextTestimonial}\n                className=\"p-2 rounded-full bg-white shadow-md hover:shadow-lg transition-shadow\"\n              >\n                <ChevronRight className=\"w-6 h-6 text-gray-600\" />\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Stats Section */}\n        <div className=\"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\">\n          <div className=\"text-center\">\n            <div className=\"text-4xl font-bold text-blue-600 mb-2\">760,000+</div>\n            <div className=\"text-gray-600\">Students Assisted</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-4xl font-bold text-green-600 mb-2\">98%</div>\n            <div className=\"text-gray-600\">Visa Success Rate</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-4xl font-bold text-purple-600 mb-2\">15+</div>\n            <div className=\"text-gray-600\">Years of Excellence</div>\n          </div>\n        </div>\n\n        {/* CTA */}\n        <div className=\"text-center mt-12\">\n          <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n            Ready to write your success story?\n          </h3>\n          <p className=\"text-gray-600 mb-6 max-w-2xl mx-auto\">\n            Join thousands of students who have achieved their study abroad dreams with AECC's expert guidance.\n          </p>\n          <button className=\"bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors shadow-md\">\n            Start Your Journey Today\n          </button>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default TestimonialsSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;;;AAHA;;;AAKA,MAAM,sBAAsB;;IAC1B,MAAM,eAAe;QACnB;YACE,MAAM;YACN,aAAa;YACb,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,YAAY;QACd;QACA;YACE,MAAM;YACN,aAAa;YACb,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,YAAY;QACd;QACA;YACE,MAAM;YACN,aAAa;YACb,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,YAAY;QACd;QACA;YACE,MAAM;YACN,aAAa;YACb,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,YAAY;QACd;KACD;IAED,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,kBAAkB;QACtB,gBAAgB,CAAC,YAAc,CAAC,YAAY,CAAC,IAAI,aAAa,MAAM;IACtE;IAEA,MAAM,kBAAkB;QACtB,gBAAgB,CAAC,YAAc,CAAC,YAAY,IAAI,aAAa,MAAM,IAAI,aAAa,MAAM;IAC5F;IAEA,MAAM,qBAAqB,YAAY,CAAC,aAAa;IAErD,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAGnB,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DACb,mBAAmB,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;0DAIpC,6LAAC;gDAAG,WAAU;0DACX,mBAAmB,IAAI;;;;;;0DAE1B,6LAAC;gDAAE,WAAU;0DACV,mBAAmB,WAAW;;;;;;0DAEjC,6LAAC;gDAAE,WAAU;0DACV,mBAAmB,MAAM;;;;;;0DAE5B,6LAAC;gDAAE,WAAU;0DACV,mBAAmB,UAAU;;;;;;0DAIhC,6LAAC;gDAAI,WAAU;0DACZ;uDAAI,MAAM,mBAAmB,MAAM;iDAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBAC7C,6LAAC,qMAAA,CAAA,OAAI;wDAAS,WAAU;uDAAb;;;;;;;;;;;;;;;;kDAMjB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAW,WAAU;;gDAA+C;gDACjE,mBAAmB,IAAI;gDAAC;;;;;;;;;;;;;;;;;;0CAMhC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAIzB,6LAAC;wCAAI,WAAU;kDACZ,aAAa,GAAG,CAAC,CAAC,GAAG,sBACpB,6LAAC;gDAEC,SAAS,IAAM,gBAAgB;gDAC/B,WAAW,AAAC,0CAEX,OADC,UAAU,eAAe,gBAAgB;+CAHtC;;;;;;;;;;kDASX,6LAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOhC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAwC;;;;;;8CACvD,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;sCAEjC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAyC;;;;;;8CACxD,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;sCAEjC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAA0C;;;;;;8CACzD,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;8BAKnC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,6LAAC;4BAAE,WAAU;sCAAuC;;;;;;sCAGpD,6LAAC;4BAAO,WAAU;sCAA0G;;;;;;;;;;;;;;;;;;;;;;;AAOtI;GA9KM;KAAA;uCAgLS", "debugId": null}}, {"offset": {"line": 1547, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/consultancy/aecc-clone/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 1611, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/consultancy/aecc-clone/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 1644, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/consultancy/aecc-clone/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,KAAyD;QAAzD,EAAE,SAAS,EAAE,GAAG,OAAyC,GAAzD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 1676, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/consultancy/aecc-clone/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,KAKgC;QALhC,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD,GALhC;IAMrB,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,KAIgC;QAJhC,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C,GAJhC;IAKlB,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,SAAS,EACT,GAAG,OACoD,GAHhC;IAIvB,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,KAGgC;QAHhC,EAC5B,SAAS,EACT,GAAG,OACyD,GAHhC;IAI5B,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 1935, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/consultancy/aecc-clone/src/components/sections/ContactFormSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from 'react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Phone, Mail, MapPin, Clock } from 'lucide-react';\n\nconst ContactFormSection = () => {\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    phone: '',\n    nearestOffice: '',\n    studyDestination: '',\n    studyYear: '',\n    studyIntake: '',\n    message: ''\n  });\n\n  const handleInputChange = (field: string, value: string) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    console.log('Form submitted:', formData);\n    // Handle form submission here\n  };\n\n  return (\n    <section className=\"py-16 bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-start\">\n          {/* Left Content */}\n          <div>\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n              Better futures,<br />\n              begin with AECC\n            </h2>\n            <p className=\"text-lg text-gray-600 mb-8 leading-relaxed\">\n              We're here to support you on your study abroad journey and help you create an extraordinary future for yourself.\n            </p>\n            <p className=\"text-gray-600 mb-8\">\n              Fill the form to schedule a free consultation session. Our counselors will get in touch with you soon.\n            </p>\n\n            {/* Contact Info */}\n            <div className=\"space-y-6\">\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"bg-blue-100 rounded-lg p-3\">\n                  <Phone className=\"w-6 h-6 text-blue-600\" />\n                </div>\n                <div>\n                  <h3 className=\"font-semibold text-gray-900 mb-1\">Call Us</h3>\n                  <p className=\"text-gray-600\">+977-01-5970315</p>\n                  <p className=\"text-gray-600\">+977-01-5970316</p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"bg-green-100 rounded-lg p-3\">\n                  <Mail className=\"w-6 h-6 text-green-600\" />\n                </div>\n                <div>\n                  <h3 className=\"font-semibold text-gray-900 mb-1\">Email Us</h3>\n                  <p className=\"text-gray-600\"><EMAIL></p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"bg-purple-100 rounded-lg p-3\">\n                  <MapPin className=\"w-6 h-6 text-purple-600\" />\n                </div>\n                <div>\n                  <h3 className=\"font-semibold text-gray-900 mb-1\">Visit Us</h3>\n                  <p className=\"text-gray-600\">Kathmandu • Pokhara • Chitwan</p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"bg-orange-100 rounded-lg p-3\">\n                  <Clock className=\"w-6 h-6 text-orange-600\" />\n                </div>\n                <div>\n                  <h3 className=\"font-semibold text-gray-900 mb-1\">Office Hours</h3>\n                  <p className=\"text-gray-600\">Sun - Fri: 9:00 AM - 6:00 PM</p>\n                  <p className=\"text-gray-600\">Sat: 10:00 AM - 4:00 PM</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Right Content - Form */}\n          <div className=\"bg-white rounded-3xl p-8 shadow-xl\">\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-6 text-center\">\n              Turn your Study Abroad Dream to Degrees abroad\n            </h3>\n\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              {/* Name Fields */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <Input\n                    placeholder=\"First Name *\"\n                    value={formData.firstName}\n                    onChange={(e) => handleInputChange('firstName', e.target.value)}\n                    required\n                  />\n                </div>\n                <div>\n                  <Input\n                    placeholder=\"Last Name *\"\n                    value={formData.lastName}\n                    onChange={(e) => handleInputChange('lastName', e.target.value)}\n                    required\n                  />\n                </div>\n              </div>\n\n              {/* Email */}\n              <Input\n                type=\"email\"\n                placeholder=\"Email Address *\"\n                value={formData.email}\n                onChange={(e) => handleInputChange('email', e.target.value)}\n                required\n              />\n\n              {/* Phone */}\n              <div className=\"flex\">\n                <div className=\"bg-gray-100 px-3 py-2 rounded-l-md border border-r-0 border-gray-300 flex items-center\">\n                  <span className=\"text-gray-600\">+977</span>\n                </div>\n                <Input\n                  type=\"tel\"\n                  placeholder=\"Phone Number *\"\n                  value={formData.phone}\n                  onChange={(e) => handleInputChange('phone', e.target.value)}\n                  className=\"rounded-l-none\"\n                  required\n                />\n              </div>\n\n              {/* Nearest Office */}\n              <Select onValueChange={(value) => handleInputChange('nearestOffice', value)}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Nearest Office\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"kathmandu\">Kathmandu</SelectItem>\n                  <SelectItem value=\"pokhara\">Pokhara</SelectItem>\n                  <SelectItem value=\"chitwan\">Chitwan</SelectItem>\n                </SelectContent>\n              </Select>\n\n              {/* Study Destination */}\n              <Select onValueChange={(value) => handleInputChange('studyDestination', value)}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Preferred Study Destination\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"australia\">Australia</SelectItem>\n                  <SelectItem value=\"canada\">Canada</SelectItem>\n                  <SelectItem value=\"usa\">USA</SelectItem>\n                  <SelectItem value=\"uk\">United Kingdom</SelectItem>\n                  <SelectItem value=\"new-zealand\">New Zealand</SelectItem>\n                  <SelectItem value=\"help-decide\">Help Me Decide</SelectItem>\n                </SelectContent>\n              </Select>\n\n              {/* Study Year and Intake */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <Select onValueChange={(value) => handleInputChange('studyYear', value)}>\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Preferred Study Year\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"2025\">2025</SelectItem>\n                    <SelectItem value=\"2026\">2026</SelectItem>\n                  </SelectContent>\n                </Select>\n\n                <Select onValueChange={(value) => handleInputChange('studyIntake', value)}>\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Preferred Study Intake\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"q1\">Q1 (Jan - Mar)</SelectItem>\n                    <SelectItem value=\"q2\">Q2 (Apr - Jun)</SelectItem>\n                    <SelectItem value=\"q3\">Q3 (Jul - Sep)</SelectItem>\n                    <SelectItem value=\"q4\">Q4 (Oct - Dec)</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n\n              {/* Message */}\n              <Textarea\n                placeholder=\"Tell us about your study abroad goals...\"\n                value={formData.message}\n                onChange={(e) => handleInputChange('message', e.target.value)}\n                rows={4}\n              />\n\n              {/* Privacy Policy */}\n              <div className=\"text-sm text-gray-600\">\n                By clicking you agree to our{' '}\n                <a href=\"/privacy-policy\" className=\"text-blue-600 hover:underline\">\n                  Privacy Policy\n                </a>{' '}\n                and{' '}\n                <a href=\"/terms-of-use\" className=\"text-blue-600 hover:underline\">\n                  Terms & Conditions\n                </a>{' '}\n                *\n              </div>\n\n              {/* Submit Button */}\n              <Button type=\"submit\" className=\"w-full bg-blue-600 hover:bg-blue-700 text-white py-3 text-lg font-semibold\">\n                Get Started for Free\n              </Button>\n            </form>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default ContactFormSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AASA,MAAM,qBAAqB;;IACzB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW;QACX,UAAU;QACV,OAAO;QACP,OAAO;QACP,eAAe;QACf,kBAAkB;QAClB,WAAW;QACX,aAAa;QACb,SAAS;IACX;IAEA,MAAM,oBAAoB,CAAC,OAAe;QACxC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,QAAQ,GAAG,CAAC,mBAAmB;IAC/B,8BAA8B;IAChC;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;;oCAAoD;kDACjD,6LAAC;;;;;oCAAK;;;;;;;0CAGvB,6LAAC;gCAAE,WAAU;0CAA6C;;;;;;0CAG1D,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAKlC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEAAgB;;;;;;kEAC7B,6LAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;kDAIjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;kDAIjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;kDAIjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEAAgB;;;;;;kEAC7B,6LAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOrC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAIlE,6LAAC;gCAAK,UAAU;gCAAc,WAAU;;kDAEtC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DACC,cAAA,6LAAC,oIAAA,CAAA,QAAK;oDACJ,aAAY;oDACZ,OAAO,SAAS,SAAS;oDACzB,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;oDAC9D,QAAQ;;;;;;;;;;;0DAGZ,6LAAC;0DACC,cAAA,6LAAC,oIAAA,CAAA,QAAK;oDACJ,aAAY;oDACZ,OAAO,SAAS,QAAQ;oDACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC7D,QAAQ;;;;;;;;;;;;;;;;;kDAMd,6LAAC,oIAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,aAAY;wCACZ,OAAO,SAAS,KAAK;wCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wCAC1D,QAAQ;;;;;;kDAIV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;0DAElC,6LAAC,oIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,aAAY;gDACZ,OAAO,SAAS,KAAK;gDACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;gDAC1D,WAAU;gDACV,QAAQ;;;;;;;;;;;;kDAKZ,6LAAC,qIAAA,CAAA,SAAM;wCAAC,eAAe,CAAC,QAAU,kBAAkB,iBAAiB;;0DACnE,6LAAC,qIAAA,CAAA,gBAAa;0DACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,6LAAC,qIAAA,CAAA,gBAAa;;kEACZ,6LAAC,qIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAY;;;;;;kEAC9B,6LAAC,qIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAU;;;;;;kEAC5B,6LAAC,qIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAU;;;;;;;;;;;;;;;;;;kDAKhC,6LAAC,qIAAA,CAAA,SAAM;wCAAC,eAAe,CAAC,QAAU,kBAAkB,oBAAoB;;0DACtE,6LAAC,qIAAA,CAAA,gBAAa;0DACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,6LAAC,qIAAA,CAAA,gBAAa;;kEACZ,6LAAC,qIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAY;;;;;;kEAC9B,6LAAC,qIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAS;;;;;;kEAC3B,6LAAC,qIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,6LAAC,qIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAK;;;;;;kEACvB,6LAAC,qIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAc;;;;;;kEAChC,6LAAC,qIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAc;;;;;;;;;;;;;;;;;;kDAKpC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,eAAe,CAAC,QAAU,kBAAkB,aAAa;;kEAC/D,6LAAC,qIAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,6LAAC,qIAAA,CAAA,gBAAa;;0EACZ,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAO;;;;;;0EACzB,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAO;;;;;;;;;;;;;;;;;;0DAI7B,6LAAC,qIAAA,CAAA,SAAM;gDAAC,eAAe,CAAC,QAAU,kBAAkB,eAAe;;kEACjE,6LAAC,qIAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,6LAAC,qIAAA,CAAA,gBAAa;;0EACZ,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;0EACvB,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;0EACvB,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;0EACvB,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;kDAM7B,6LAAC,uIAAA,CAAA,WAAQ;wCACP,aAAY;wCACZ,OAAO,SAAS,OAAO;wCACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;wCAC5D,MAAM;;;;;;kDAIR,6LAAC;wCAAI,WAAU;;4CAAwB;4CACR;0DAC7B,6LAAC;gDAAE,MAAK;gDAAkB,WAAU;0DAAgC;;;;;;4CAE/D;4CAAI;4CACL;0DACJ,6LAAC;gDAAE,MAAK;gDAAgB,WAAU;0DAAgC;;;;;;4CAE7D;4CAAI;;;;;;;kDAKX,6LAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,WAAU;kDAA6E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3H;GA/NM;KAAA;uCAiOS", "debugId": null}}]}