{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/consultancy/aecc-clone/src/components/layout/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/consultancy/aecc-clone/src/components/layout/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/consultancy/aecc-clone/src/components/layout/Footer.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"container mx-auto px-4 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\">\n          {/* Logo and Social */}\n          <div className=\"lg:col-span-1\">\n            <Image\n              src=\"/images/new-logo_white.svg\"\n              alt=\"AECC Nepal\"\n              width={120}\n              height={40}\n              className=\"h-10 w-auto mb-4\"\n            />\n            <h4 className=\"text-lg font-semibold mb-4\">Let's get social.</h4>\n            <div className=\"flex space-x-4\">\n              <Link href=\"#\" className=\"text-gray-400 hover:text-white\">\n                <span className=\"sr-only\">Facebook</span>\n                <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n                </svg>\n              </Link>\n              <Link href=\"#\" className=\"text-gray-400 hover:text-white\">\n                <span className=\"sr-only\">LinkedIn</span>\n                <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n                </svg>\n              </Link>\n              <Link href=\"#\" className=\"text-gray-400 hover:text-white\">\n                <span className=\"sr-only\">Instagram</span>\n                <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387z\"/>\n                </svg>\n              </Link>\n              <Link href=\"#\" className=\"text-gray-400 hover:text-white\">\n                <span className=\"sr-only\">YouTube</span>\n                <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z\"/>\n                </svg>\n              </Link>\n            </div>\n          </div>\n\n          {/* About Us */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-4\">About Us</h4>\n            <ul className=\"space-y-2\">\n              <li><Link href=\"/about-us/who-we-are\" className=\"text-gray-400 hover:text-white\">Who We Are</Link></li>\n              <li><Link href=\"/about-us/our-timeline\" className=\"text-gray-400 hover:text-white\">Our Timeline</Link></li>\n              <li><Link href=\"/about-us/leadership-team\" className=\"text-gray-400 hover:text-white\">Our Leadership Team</Link></li>\n              <li><Link href=\"/about-us/awards-recognitions\" className=\"text-gray-400 hover:text-white\">Awards Recognitions</Link></li>\n              <li><Link href=\"/careers\" className=\"text-gray-400 hover:text-white\">Careers</Link></li>\n            </ul>\n          </div>\n\n          {/* Our Services */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-4\">Our Services</h4>\n            <ul className=\"space-y-2\">\n              <li><Link href=\"/student-services/meet-us-online\" className=\"text-gray-400 hover:text-white\">Meet us Online</Link></li>\n              <li><Link href=\"/student-services/admission-counselling\" className=\"text-gray-400 hover:text-white\">Admission Counselling</Link></li>\n              <li><Link href=\"/student-services/student-health-insurance\" className=\"text-gray-400 hover:text-white\">Health Cover</Link></li>\n              <li><Link href=\"/student-services/student-visa-service\" className=\"text-gray-400 hover:text-white\">Student Visa Service</Link></li>\n            </ul>\n          </div>\n\n          {/* Study Destinations */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-4\">Study Destinations</h4>\n            <ul className=\"space-y-2\">\n              <li><Link href=\"/study-abroad/study-in-australia\" className=\"text-gray-400 hover:text-white\">Study In Australia</Link></li>\n              <li><Link href=\"/study-abroad/study-in-canada\" className=\"text-gray-400 hover:text-white\">Study In Canada</Link></li>\n              <li><Link href=\"/study-abroad/study-in-europe\" className=\"text-gray-400 hover:text-white\">Study In Europe</Link></li>\n              <li><Link href=\"/study-abroad/study-in-new-zealand\" className=\"text-gray-400 hover:text-white\">Study In New Zealand</Link></li>\n              <li><Link href=\"/study-abroad/study-in-uk\" className=\"text-gray-400 hover:text-white\">Study In UK</Link></li>\n              <li><Link href=\"/study-abroad/study-in-usa\" className=\"text-gray-400 hover:text-white\">Study In USA</Link></li>\n            </ul>\n          </div>\n\n          {/* Our Branches */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-4\">Our Branches</h4>\n            <ul className=\"space-y-2\">\n              <li><Link href=\"/study-abroad-consultant/kathmandu\" className=\"text-gray-400 hover:text-white\">Kathmandu</Link></li>\n              <li><Link href=\"/study-abroad-consultant/pokhara\" className=\"text-gray-400 hover:text-white\">Pokhara</Link></li>\n              <li><Link href=\"/study-abroad-consultant/chitwan\" className=\"text-gray-400 hover:text-white\">Chitwan</Link></li>\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom section */}\n        <div className=\"border-t border-gray-800 mt-8 pt-8\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <p className=\"text-gray-400 text-sm\">\n              Copyright © 2025 aecc. All rights reserved.\n            </p>\n            <div className=\"flex space-x-4 mt-4 md:mt-0\">\n              <Link href=\"/terms-of-use\" className=\"text-gray-400 hover:text-white text-sm\">Terms of Use</Link>\n              <Link href=\"/privacy-policy\" className=\"text-gray-400 hover:text-white text-sm\">Privacy Policy</Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;8CAEZ,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAuB,WAAU;0DAAiC;;;;;;;;;;;sDACjF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAyB,WAAU;0DAAiC;;;;;;;;;;;sDACnF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAA4B,WAAU;0DAAiC;;;;;;;;;;;sDACtF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAgC,WAAU;0DAAiC;;;;;;;;;;;sDAC1F,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAiC;;;;;;;;;;;;;;;;;;;;;;;sCAKzE,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmC,WAAU;0DAAiC;;;;;;;;;;;sDAC7F,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAA0C,WAAU;0DAAiC;;;;;;;;;;;sDACpG,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAA6C,WAAU;0DAAiC;;;;;;;;;;;sDACvG,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAyC,WAAU;0DAAiC;;;;;;;;;;;;;;;;;;;;;;;sCAKvG,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmC,WAAU;0DAAiC;;;;;;;;;;;sDAC7F,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAgC,WAAU;0DAAiC;;;;;;;;;;;sDAC1F,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAgC,WAAU;0DAAiC;;;;;;;;;;;sDAC1F,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqC,WAAU;0DAAiC;;;;;;;;;;;sDAC/F,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAA4B,WAAU;0DAAiC;;;;;;;;;;;sDACtF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAA6B,WAAU;0DAAiC;;;;;;;;;;;;;;;;;;;;;;;sCAK3F,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqC,WAAU;0DAAiC;;;;;;;;;;;sDAC/F,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmC,WAAU;0DAAiC;;;;;;;;;;;sDAC7F,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmC,WAAU;0DAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMnG,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;0CAGrC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAgB,WAAU;kDAAyC;;;;;;kDAC9E,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAkB,WAAU;kDAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO9F;uCAEe", "debugId": null}}, {"offset": {"line": 685, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/consultancy/aecc-clone/src/components/sections/HeroSection.tsx"], "sourcesContent": ["import React from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\n\nconst HeroSection = () => {\n  return (\n    <section className=\"relative bg-gradient-to-br from-blue-50 to-indigo-100 overflow-hidden\">\n      <div className=\"container mx-auto px-4 py-16 lg:py-24\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Content */}\n          <div className=\"text-center lg:text-left\">\n            <h1 className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight\">\n              Nepal's leading study abroad consultants\n            </h1>\n            <p className=\"text-xl md:text-2xl text-gray-600 mb-8\">\n              We've assisted over 760,000 students in their study abroad journey.\n            </p>\n            \n            {/* Country Buttons */}\n            <div className=\"flex flex-wrap justify-center lg:justify-start gap-3 mb-8\">\n              <Link \n                href=\"/study-abroad/study-in-australia\"\n                className=\"bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors shadow-md border border-blue-100\"\n              >\n                Australia\n              </Link>\n              <Link \n                href=\"/study-abroad/study-in-uk\"\n                className=\"bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors shadow-md border border-blue-100\"\n              >\n                UK\n              </Link>\n              <Link \n                href=\"/study-abroad/study-in-canada\"\n                className=\"bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors shadow-md border border-blue-100\"\n              >\n                Canada\n              </Link>\n              <Link \n                href=\"/study-abroad/study-in-usa\"\n                className=\"bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors shadow-md border border-blue-100\"\n              >\n                USA\n              </Link>\n              <Link \n                href=\"/study-abroad/study-in-new-zealand\"\n                className=\"bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors shadow-md border border-blue-100\"\n              >\n                New Zealand\n              </Link>\n            </div>\n          </div>\n\n          {/* Right Content - Hero Image */}\n          <div className=\"relative\">\n            <div className=\"relative z-10\">\n              {/* Placeholder for hero image */}\n              <div className=\"bg-gradient-to-br from-blue-400 to-indigo-600 rounded-2xl p-8 text-white text-center shadow-2xl\">\n                <div className=\"mb-6\">\n                  <svg className=\"w-24 h-24 mx-auto mb-4\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z\"/>\n                  </svg>\n                </div>\n                <h3 className=\"text-2xl font-bold mb-2\">Study Abroad Consultancy</h3>\n                <p className=\"text-blue-100\">Your trusted partner for international education</p>\n              </div>\n            </div>\n            \n            {/* Background decorative elements */}\n            <div className=\"absolute -top-4 -right-4 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob\"></div>\n            <div className=\"absolute -bottom-8 -left-4 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000\"></div>\n            <div className=\"absolute -bottom-4 left-20 w-72 h-72 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000\"></div>\n          </div>\n        </div>\n      </div>\n      \n      {/* Background pattern */}\n      <div className=\"absolute inset-0 bg-grid-pattern opacity-5\"></div>\n    </section>\n  );\n};\n\nexport default HeroSection;\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,MAAM,cAAc;IAClB,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA8E;;;;;;8CAG5F,8OAAC;oCAAE,WAAU;8CAAyC;;;;;;8CAKtD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;sCAOL,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAEb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAyB,MAAK;oDAAe,SAAQ;8DAClE,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;0DAGZ,8OAAC;gDAAG,WAAU;0DAA0B;;;;;;0DACxC,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;8CAKjC,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMrB,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;uCAEe", "debugId": null}}, {"offset": {"line": 895, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/consultancy/aecc-clone/src/app/page.tsx"], "sourcesContent": ["import Header from '@/components/layout/Header';\nimport Footer from '@/components/layout/Footer';\nimport HeroSection from '@/components/sections/HeroSection';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen flex flex-col\">\n      <Header />\n      <main className=\"flex-1\">\n        <HeroSection />\n\n        {/* Placeholder for other sections */}\n        <section className=\"py-16\">\n          <div className=\"container mx-auto px-4 text-center\">\n            <h2 className=\"text-3xl font-bold mb-8\">More sections coming soon...</h2>\n            <p className=\"text-gray-600\">\n              We're building the complete AECC Global website clone with all sections.\n            </p>\n          </div>\n        </section>\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAK,WAAU;;kCACd,8OAAC,6IAAA,CAAA,UAAW;;;;;kCAGZ,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0B;;;;;;8CACxC,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;0BAMnC,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}]}