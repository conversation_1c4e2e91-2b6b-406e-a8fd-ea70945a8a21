{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/consultancy/aecc-clone/src/components/layout/Header.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\n\nconst Header = () => {\n  return (\n    <header className=\"bg-white shadow-sm\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center\">\n            <Image\n              src=\"/images/new-logo.svg\"\n              alt=\"AECC Nepal\"\n              width={120}\n              height={40}\n              className=\"h-10 w-auto\"\n            />\n          </Link>\n\n          {/* Navigation - Desktop */}\n          <nav className=\"hidden lg:flex items-center space-x-8\">\n            <Link href=\"/student-services\" className=\"text-gray-700 hover:text-blue-600\">\n              Student Services\n            </Link>\n            <Link href=\"/study-abroad\" className=\"text-gray-700 hover:text-blue-600\">\n              Study Abroad\n            </Link>\n            <Link href=\"/scholarships\" className=\"text-gray-700 hover:text-blue-600\">\n              Scholarships\n            </Link>\n            <Link href=\"/test-preparation\" className=\"text-gray-700 hover:text-blue-600\">\n              Test Preparation\n            </Link>\n            <Link href=\"/success-stories\" className=\"text-gray-700 hover:text-blue-600\">\n              Success Stories\n            </Link>\n            <Link href=\"/upcoming-events\" className=\"text-gray-700 hover:text-blue-600\">\n              Upcoming Events\n            </Link>\n            <Link href=\"/blog\" className=\"text-gray-700 hover:text-blue-600\">\n              Blog\n            </Link>\n          </nav>\n\n          {/* CTA Button */}\n          <div className=\"hidden lg:block\">\n            <button className=\"bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors\">\n              Meet us Online\n            </button>\n          </div>\n\n          {/* Mobile menu button */}\n          <button className=\"lg:hidden\">\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n            </svg>\n          </button>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAMA,MAAM,SAAS;IACb,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;kCACvB,cAAA,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;;;;;;kCAKd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAoB,WAAU;0CAAoC;;;;;;0CAG7E,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAgB,WAAU;0CAAoC;;;;;;0CAGzE,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAgB,WAAU;0CAAoC;;;;;;0CAGzE,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAoB,WAAU;0CAAoC;;;;;;0CAG7E,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAmB,WAAU;0CAAoC;;;;;;0CAG5E,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAmB,WAAU;0CAAoC;;;;;;0CAG5E,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAQ,WAAU;0CAAoC;;;;;;;;;;;;kCAMnE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAO,WAAU;sCAAkF;;;;;;;;;;;kCAMtG,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnF;KA1DM;uCA4DS", "debugId": null}}]}