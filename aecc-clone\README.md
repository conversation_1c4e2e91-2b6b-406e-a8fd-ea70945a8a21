# AECC Global Nepal - Website Clone

A modern, responsive clone of the AECC Global Nepal website built with Next.js, Tailwind CSS, and shadcn/ui components.

## 🌟 Features

- **Modern Tech Stack**: Built with Next.js 15, TypeScript, and Tailwind CSS
- **Responsive Design**: Fully responsive across all devices
- **Component Library**: Uses shadcn/ui for consistent, accessible components
- **Interactive Elements**: Dropdown menus, carousels, and form interactions
- **Performance Optimized**: Fast loading with Next.js optimizations
- **Accessibility**: WCAG compliant with proper focus management

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd aecc-clone
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📁 Project Structure

```
src/
├── app/                    # Next.js app directory
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/
│   ├── layout/            # Layout components
│   │   ├── Header.tsx     # Navigation header
│   │   └── Footer.tsx     # Site footer
│   ├── sections/          # Page sections
│   │   ├── HeroSection.tsx
│   │   ├── EventsSection.tsx
│   │   ├── ServicesSection.tsx
│   │   ├── AboutSection.tsx
│   │   ├── StudyDestinationsSection.tsx
│   │   ├── TestimonialsSection.tsx
│   │   ├── PartnershipsSection.tsx
│   │   └── ContactFormSection.tsx
│   └── ui/                # Reusable UI components
│       ├── BackToTop.tsx
│       ├── LoadingSpinner.tsx
│       └── [shadcn components]
└── lib/
    └── utils.ts           # Utility functions
```

## 🎨 Design Features

- **Hero Section**: Eye-catching gradient background with country selection
- **Events Section**: Interactive city cards for event locations
- **Services Section**: Comprehensive service offerings with icons
- **About Section**: Company information with statistics
- **Study Destinations**: Country cards with highlights
- **Testimonials**: Interactive carousel with student reviews
- **Partnerships**: Industry partnerships and accreditations
- **Contact Form**: Complete consultation booking form

## 🛠️ Technologies Used

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Components**: shadcn/ui
- **Icons**: Lucide React
- **Fonts**: Inter (Google Fonts)

## 📱 Responsive Design

The website is fully responsive with:
- Mobile-first approach
- Sticky navigation header
- Collapsible mobile menu
- Touch-friendly interactions
- Optimized typography scaling

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 📄 License

This project is for educational purposes only. All content and design elements are inspired by the original AECC Global Nepal website.

## 🤝 Contributing

This is a demonstration project. For the actual AECC Global website, please visit [aeccglobal.com.np](https://www.aeccglobal.com.np/).

## 📞 Contact

For questions about this clone project, please refer to the original AECC Global Nepal website for official information and services.
