{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/consultancy/aecc-clone/src/components/layout/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/consultancy/aecc-clone/src/components/layout/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/consultancy/aecc-clone/src/components/layout/Footer.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"container mx-auto px-4 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\">\n          {/* Logo and Social */}\n          <div className=\"lg:col-span-1\">\n            <Image\n              src=\"/images/new-logo_white.svg\"\n              alt=\"AECC Nepal\"\n              width={120}\n              height={40}\n              className=\"h-10 w-auto mb-4\"\n            />\n            <h4 className=\"text-lg font-semibold mb-4\">Let's get social.</h4>\n            <div className=\"flex space-x-4\">\n              <Link href=\"#\" className=\"text-gray-400 hover:text-white\">\n                <span className=\"sr-only\">Facebook</span>\n                <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n                </svg>\n              </Link>\n              <Link href=\"#\" className=\"text-gray-400 hover:text-white\">\n                <span className=\"sr-only\">LinkedIn</span>\n                <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n                </svg>\n              </Link>\n              <Link href=\"#\" className=\"text-gray-400 hover:text-white\">\n                <span className=\"sr-only\">Instagram</span>\n                <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387z\"/>\n                </svg>\n              </Link>\n              <Link href=\"#\" className=\"text-gray-400 hover:text-white\">\n                <span className=\"sr-only\">YouTube</span>\n                <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z\"/>\n                </svg>\n              </Link>\n            </div>\n          </div>\n\n          {/* About Us */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-4\">About Us</h4>\n            <ul className=\"space-y-2\">\n              <li><Link href=\"/about-us/who-we-are\" className=\"text-gray-400 hover:text-white\">Who We Are</Link></li>\n              <li><Link href=\"/about-us/our-timeline\" className=\"text-gray-400 hover:text-white\">Our Timeline</Link></li>\n              <li><Link href=\"/about-us/leadership-team\" className=\"text-gray-400 hover:text-white\">Our Leadership Team</Link></li>\n              <li><Link href=\"/about-us/awards-recognitions\" className=\"text-gray-400 hover:text-white\">Awards Recognitions</Link></li>\n              <li><Link href=\"/careers\" className=\"text-gray-400 hover:text-white\">Careers</Link></li>\n            </ul>\n          </div>\n\n          {/* Our Services */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-4\">Our Services</h4>\n            <ul className=\"space-y-2\">\n              <li><Link href=\"/student-services/meet-us-online\" className=\"text-gray-400 hover:text-white\">Meet us Online</Link></li>\n              <li><Link href=\"/student-services/admission-counselling\" className=\"text-gray-400 hover:text-white\">Admission Counselling</Link></li>\n              <li><Link href=\"/student-services/student-health-insurance\" className=\"text-gray-400 hover:text-white\">Health Cover</Link></li>\n              <li><Link href=\"/student-services/student-visa-service\" className=\"text-gray-400 hover:text-white\">Student Visa Service</Link></li>\n            </ul>\n          </div>\n\n          {/* Study Destinations */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-4\">Study Destinations</h4>\n            <ul className=\"space-y-2\">\n              <li><Link href=\"/study-abroad/study-in-australia\" className=\"text-gray-400 hover:text-white\">Study In Australia</Link></li>\n              <li><Link href=\"/study-abroad/study-in-canada\" className=\"text-gray-400 hover:text-white\">Study In Canada</Link></li>\n              <li><Link href=\"/study-abroad/study-in-europe\" className=\"text-gray-400 hover:text-white\">Study In Europe</Link></li>\n              <li><Link href=\"/study-abroad/study-in-new-zealand\" className=\"text-gray-400 hover:text-white\">Study In New Zealand</Link></li>\n              <li><Link href=\"/study-abroad/study-in-uk\" className=\"text-gray-400 hover:text-white\">Study In UK</Link></li>\n              <li><Link href=\"/study-abroad/study-in-usa\" className=\"text-gray-400 hover:text-white\">Study In USA</Link></li>\n            </ul>\n          </div>\n\n          {/* Our Branches */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-4\">Our Branches</h4>\n            <ul className=\"space-y-2\">\n              <li><Link href=\"/study-abroad-consultant/kathmandu\" className=\"text-gray-400 hover:text-white\">Kathmandu</Link></li>\n              <li><Link href=\"/study-abroad-consultant/pokhara\" className=\"text-gray-400 hover:text-white\">Pokhara</Link></li>\n              <li><Link href=\"/study-abroad-consultant/chitwan\" className=\"text-gray-400 hover:text-white\">Chitwan</Link></li>\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom section */}\n        <div className=\"border-t border-gray-800 mt-8 pt-8\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <p className=\"text-gray-400 text-sm\">\n              Copyright © 2025 aecc. All rights reserved.\n            </p>\n            <div className=\"flex space-x-4 mt-4 md:mt-0\">\n              <Link href=\"/terms-of-use\" className=\"text-gray-400 hover:text-white text-sm\">Terms of Use</Link>\n              <Link href=\"/privacy-policy\" className=\"text-gray-400 hover:text-white text-sm\">Privacy Policy</Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;8CAEZ,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAuB,WAAU;0DAAiC;;;;;;;;;;;sDACjF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAyB,WAAU;0DAAiC;;;;;;;;;;;sDACnF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAA4B,WAAU;0DAAiC;;;;;;;;;;;sDACtF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAgC,WAAU;0DAAiC;;;;;;;;;;;sDAC1F,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAiC;;;;;;;;;;;;;;;;;;;;;;;sCAKzE,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmC,WAAU;0DAAiC;;;;;;;;;;;sDAC7F,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAA0C,WAAU;0DAAiC;;;;;;;;;;;sDACpG,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAA6C,WAAU;0DAAiC;;;;;;;;;;;sDACvG,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAyC,WAAU;0DAAiC;;;;;;;;;;;;;;;;;;;;;;;sCAKvG,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmC,WAAU;0DAAiC;;;;;;;;;;;sDAC7F,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAgC,WAAU;0DAAiC;;;;;;;;;;;sDAC1F,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAgC,WAAU;0DAAiC;;;;;;;;;;;sDAC1F,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqC,WAAU;0DAAiC;;;;;;;;;;;sDAC/F,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAA4B,WAAU;0DAAiC;;;;;;;;;;;sDACtF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAA6B,WAAU;0DAAiC;;;;;;;;;;;;;;;;;;;;;;;sCAK3F,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqC,WAAU;0DAAiC;;;;;;;;;;;sDAC/F,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmC,WAAU;0DAAiC;;;;;;;;;;;sDAC7F,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmC,WAAU;0DAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMnG,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;0CAGrC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAgB,WAAU;kDAAyC;;;;;;kDAC9E,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAkB,WAAU;kDAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO9F;uCAEe", "debugId": null}}, {"offset": {"line": 685, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/consultancy/aecc-clone/src/components/sections/HeroSection.tsx"], "sourcesContent": ["import React from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\n\nconst HeroSection = () => {\n  return (\n    <section className=\"relative bg-gradient-to-br from-blue-50 to-indigo-100 overflow-hidden\">\n      <div className=\"container mx-auto px-4 py-16 lg:py-24\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Content */}\n          <div className=\"text-center lg:text-left\">\n            <h1 className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight\">\n              Nepal's leading study abroad consultants\n            </h1>\n            <p className=\"text-xl md:text-2xl text-gray-600 mb-8\">\n              We've assisted over 760,000 students in their study abroad journey.\n            </p>\n            \n            {/* Country Buttons */}\n            <div className=\"flex flex-wrap justify-center lg:justify-start gap-3 mb-8\">\n              <Link \n                href=\"/study-abroad/study-in-australia\"\n                className=\"bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors shadow-md border border-blue-100\"\n              >\n                Australia\n              </Link>\n              <Link \n                href=\"/study-abroad/study-in-uk\"\n                className=\"bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors shadow-md border border-blue-100\"\n              >\n                UK\n              </Link>\n              <Link \n                href=\"/study-abroad/study-in-canada\"\n                className=\"bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors shadow-md border border-blue-100\"\n              >\n                Canada\n              </Link>\n              <Link \n                href=\"/study-abroad/study-in-usa\"\n                className=\"bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors shadow-md border border-blue-100\"\n              >\n                USA\n              </Link>\n              <Link \n                href=\"/study-abroad/study-in-new-zealand\"\n                className=\"bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors shadow-md border border-blue-100\"\n              >\n                New Zealand\n              </Link>\n            </div>\n          </div>\n\n          {/* Right Content - Hero Image */}\n          <div className=\"relative\">\n            <div className=\"relative z-10\">\n              {/* Placeholder for hero image */}\n              <div className=\"bg-gradient-to-br from-blue-400 to-indigo-600 rounded-2xl p-8 text-white text-center shadow-2xl\">\n                <div className=\"mb-6\">\n                  <svg className=\"w-24 h-24 mx-auto mb-4\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z\"/>\n                  </svg>\n                </div>\n                <h3 className=\"text-2xl font-bold mb-2\">Study Abroad Consultancy</h3>\n                <p className=\"text-blue-100\">Your trusted partner for international education</p>\n              </div>\n            </div>\n            \n            {/* Background decorative elements */}\n            <div className=\"absolute -top-4 -right-4 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob\"></div>\n            <div className=\"absolute -bottom-8 -left-4 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000\"></div>\n            <div className=\"absolute -bottom-4 left-20 w-72 h-72 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000\"></div>\n          </div>\n        </div>\n      </div>\n      \n      {/* Background pattern */}\n      <div className=\"absolute inset-0 bg-grid-pattern opacity-5\"></div>\n    </section>\n  );\n};\n\nexport default HeroSection;\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,MAAM,cAAc;IAClB,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA8E;;;;;;8CAG5F,8OAAC;oCAAE,WAAU;8CAAyC;;;;;;8CAKtD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;sCAOL,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAEb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAyB,MAAK;oDAAe,SAAQ;8DAClE,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;0DAGZ,8OAAC;gDAAG,WAAU;0DAA0B;;;;;;0DACxC,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;8CAKjC,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMrB,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;uCAEe", "debugId": null}}, {"offset": {"line": 895, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/consultancy/aecc-clone/src/components/sections/EventsSection.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { MapPin, Calendar } from 'lucide-react';\n\nconst EventsSection = () => {\n  const cities = [\n    {\n      name: 'Kathmandu',\n      href: '/upcoming-event/kathmandu',\n      image: '/images/kathmandu-bg.jpg',\n      description: 'Capital city events and seminars'\n    },\n    {\n      name: '<PERSON><PERSON><PERSON>',\n      href: '/upcoming-event/chitwan',\n      image: '/images/chitwan-bg.jpg',\n      description: 'Regional consultation sessions'\n    },\n    {\n      name: 'Pokhara',\n      href: '/upcoming-event/pokhara',\n      image: '/images/pokhara-bg.jpg',\n      description: 'Study abroad information sessions'\n    }\n  ];\n\n  return (\n    <section className=\"py-16 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            Find events near you\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            From your hometown to the world: Find study abroad events in your city today\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\">\n          {cities.map((city, index) => (\n            <Link\n              key={city.name}\n              href={city.href}\n              className=\"group relative overflow-hidden rounded-2xl bg-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2\"\n            >\n              {/* City Image Background */}\n              <div className=\"relative h-48 bg-gradient-to-br from-blue-400 to-indigo-600 overflow-hidden\">\n                {/* Placeholder gradient background */}\n                <div className=\"absolute inset-0 bg-gradient-to-br from-blue-400 to-indigo-600\"></div>\n                \n                {/* City Icon */}\n                <div className=\"absolute inset-0 flex items-center justify-center\">\n                  <div className=\"bg-white/20 backdrop-blur-sm rounded-full p-6\">\n                    <MapPin className=\"w-12 h-12 text-white\" />\n                  </div>\n                </div>\n\n                {/* Overlay */}\n                <div className=\"absolute inset-0 bg-black/20 group-hover:bg-black/30 transition-colors\"></div>\n              </div>\n\n              {/* Content */}\n              <div className=\"p-6\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <h3 className=\"text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors\">\n                    {city.name}\n                  </h3>\n                  <Calendar className=\"w-5 h-5 text-gray-400\" />\n                </div>\n                <p className=\"text-gray-600 text-sm mb-4\">\n                  {city.description}\n                </p>\n                <div className=\"flex items-center text-blue-600 font-medium text-sm\">\n                  <span>View Events</span>\n                  <svg className=\"w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                  </svg>\n                </div>\n              </div>\n\n              {/* Decorative element */}\n              <div className=\"absolute top-4 right-4 w-8 h-8 bg-white/20 rounded-full\"></div>\n              <div className=\"absolute top-6 right-6 w-4 h-4 bg-white/30 rounded-full\"></div>\n            </Link>\n          ))}\n        </div>\n\n        {/* CTA Section */}\n        <div className=\"text-center mt-12\">\n          <div className=\"bg-white rounded-2xl p-8 shadow-lg max-w-2xl mx-auto\">\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n              Can't find an event in your city?\n            </h3>\n            <p className=\"text-gray-600 mb-6\">\n              Book a free online consultation with our expert counselors\n            </p>\n            <button className=\"bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors shadow-md\">\n              Book Free Consultation\n            </button>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default EventsSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;;;;AAEA,MAAM,gBAAgB;IACpB,MAAM,SAAS;QACb;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,8OAAC;oBAAI,WAAU;8BACZ,OAAO,GAAG,CAAC,CAAC,MAAM,sBACjB,8OAAC,4JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;;;;;sDAGf,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAKtB,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAIjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACX,KAAK,IAAI;;;;;;8DAEZ,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;sDAEtB,8OAAC;4CAAE,WAAU;sDACV,KAAK,WAAW;;;;;;sDAEnB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;oDAAI,WAAU;oDAA8D,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACrH,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;8CAM3E,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;2BAzCV,KAAK,IAAI;;;;;;;;;;8BA+CpB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,8OAAC;gCAAO,WAAU;0CAA0G;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxI;uCAEe", "debugId": null}}, {"offset": {"line": 1165, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/consultancy/aecc-clone/src/components/sections/ServicesSection.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { \n  BookOpen, \n  GraduationCap, \n  FileText, \n  Users, \n  School, \n  Home, \n  Shield \n} from 'lucide-react';\n\nconst ServicesSection = () => {\n  const services = [\n    {\n      icon: BookOpen,\n      title: 'Test Preparation',\n      description: 'IELTS, TOEFL, PTE, GRE, SAT preparation programs',\n      href: '/english-proficiency-coaching',\n      color: 'bg-blue-500'\n    },\n    {\n      icon: GraduationCap,\n      title: 'Scholarship Guidance',\n      description: 'Find and apply for scholarships worldwide',\n      href: '/scholarships',\n      color: 'bg-green-500'\n    },\n    {\n      icon: FileText,\n      title: 'Student Visa Service',\n      description: 'Complete visa application assistance',\n      href: '/student-services/student-visa-service',\n      color: 'bg-purple-500'\n    },\n    {\n      icon: Users,\n      title: 'Education Counseling',\n      description: 'Personalized admission counselling',\n      href: '/student-services/admission-counselling',\n      color: 'bg-orange-500'\n    },\n    {\n      icon: School,\n      title: 'University Admission',\n      description: 'Application support for top universities',\n      href: '/study-abroad',\n      color: 'bg-red-500'\n    },\n    {\n      icon: Home,\n      title: 'Student Accommodation',\n      description: 'Find the perfect place to stay',\n      href: '/student-services/student-accommodation',\n      color: 'bg-indigo-500'\n    },\n    {\n      icon: Shield,\n      title: 'Student Health Insurance',\n      description: 'Comprehensive health coverage',\n      href: '/student-services/student-health-insurance',\n      color: 'bg-teal-500'\n    }\n  ];\n\n  return (\n    <section className=\"py-16 bg-white\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            With you at every step of your<br />\n            study abroad journey\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto mb-8\">\n            Get personalised, friendly, honest guidance for free<br />\n            from the top consultancy in Nepal\n          </p>\n          <button className=\"bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors shadow-md\">\n            Book a free consultation\n          </button>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mt-16\">\n          {services.map((service, index) => {\n            const IconComponent = service.icon;\n            return (\n              <Link\n                key={service.title}\n                href={service.href}\n                className=\"group relative bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100\"\n              >\n                {/* Icon */}\n                <div className={`${service.color} w-16 h-16 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform`}>\n                  <IconComponent className=\"w-8 h-8 text-white\" />\n                </div>\n\n                {/* Content */}\n                <h3 className=\"text-lg font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors\">\n                  {service.title}\n                </h3>\n                <p className=\"text-gray-600 text-sm leading-relaxed\">\n                  {service.description}\n                </p>\n\n                {/* Arrow indicator */}\n                <div className=\"absolute top-6 right-6 opacity-0 group-hover:opacity-100 transition-opacity\">\n                  <svg className=\"w-5 h-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                  </svg>\n                </div>\n\n                {/* Decorative gradient */}\n                <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50/50 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity\"></div>\n              </Link>\n            );\n          })}\n        </div>\n\n        {/* Bottom CTA */}\n        <div className=\"text-center mt-16\">\n          <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 max-w-4xl mx-auto\">\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n              Ready to start your study abroad journey?\n            </h3>\n            <p className=\"text-gray-600 mb-6 max-w-2xl mx-auto\">\n              Join thousands of students who have successfully achieved their dreams of studying abroad with AECC's expert guidance.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button className=\"bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors shadow-md\">\n                Get Started Today\n              </button>\n              <button className=\"border border-blue-600 text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors\">\n                Download Brochure\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default ServicesSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;AAUA,MAAM,kBAAkB;IACtB,MAAM,WAAW;QACf;YACE,MAAM,8MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM,wNAAA,CAAA,gBAAa;YACnB,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM,8MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YAC<PERSON>,aAAa;YAC<PERSON>,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM,mMAAA,CAAA,OAAI;YACV,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAoD;8CAClC,8OAAC;;;;;gCAAK;;;;;;;sCAGtC,8OAAC;4BAAE,WAAU;;gCAA+C;8CACN,8OAAC;;;;;gCAAK;;;;;;;sCAG5D,8OAAC;4BAAO,WAAU;sCAA0G;;;;;;;;;;;;8BAK9H,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS;wBACtB,MAAM,gBAAgB,QAAQ,IAAI;wBAClC,qBACE,8OAAC,4JAAA,CAAA,UAAI;4BAEH,MAAM,QAAQ,IAAI;4BAClB,WAAU;;8CAGV,8OAAC;oCAAI,WAAW,GAAG,QAAQ,KAAK,CAAC,uGAAuG,CAAC;8CACvI,cAAA,8OAAC;wCAAc,WAAU;;;;;;;;;;;8CAI3B,8OAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;8CAEhB,8OAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;8CAItB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAwB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC/E,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAKzE,8OAAC;oCAAI,WAAU;;;;;;;2BAzBV,QAAQ,KAAK;;;;;oBA4BxB;;;;;;8BAIF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAGpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAA0G;;;;;;kDAG5H,8OAAC;wCAAO,WAAU;kDAA6G;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7I;uCAEe", "debugId": null}}, {"offset": {"line": 1444, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/consultancy/aecc-clone/src/components/sections/AboutSection.tsx"], "sourcesContent": ["import React from 'react';\nimport { CheckCir<PERSON>, Users, Globe, Award } from 'lucide-react';\n\nconst AboutSection = () => {\n  const stats = [\n    {\n      icon: Users,\n      number: '760,000+',\n      label: 'Students Assisted',\n      description: 'Successfully guided students to their dream destinations'\n    },\n    {\n      icon: Globe,\n      number: '15+',\n      label: 'Years of Experience',\n      description: 'Serving students since 2008 with excellence'\n    },\n    {\n      icon: Award,\n      number: '100+',\n      label: 'Partner Universities',\n      description: 'Strong partnerships with top institutions worldwide'\n    }\n  ];\n\n  const features = [\n    'Free consultation and guidance',\n    'Expert visa application support',\n    'Scholarship assistance',\n    'Test preparation programs',\n    'Accommodation support',\n    'Post-arrival services'\n  ];\n\n  return (\n    <section className=\"py-16 bg-white\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Content */}\n          <div>\n            <div className=\"mb-8\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                Bridging the gap between students and their study abroad dreams since 2008\n              </h2>\n              <p className=\"text-lg text-gray-600 leading-relaxed mb-6\">\n                At AECC, we take pride in offering an unparalleled consultation experience. We strive to become the best education consultancy in Nepal. To do so, our consultants ensure a seamless process and earnestly commit to meeting your study abroad needs from start to finish.\n              </p>\n              <p className=\"text-gray-600 leading-relaxed mb-8\">\n                Our dedicated team of experienced counselors provides personalized guidance to help you choose the right course, university, and destination that aligns with your career goals and aspirations.\n              </p>\n            </div>\n\n            {/* Features List */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-8\">\n              {features.map((feature, index) => (\n                <div key={index} className=\"flex items-center space-x-3\">\n                  <CheckCircle className=\"w-5 h-5 text-green-500 flex-shrink-0\" />\n                  <span className=\"text-gray-700\">{feature}</span>\n                </div>\n              ))}\n            </div>\n\n            {/* CTA Button */}\n            <button className=\"bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors shadow-md\">\n              Talk to a counsellor\n            </button>\n          </div>\n\n          {/* Right Content - Stats */}\n          <div className=\"relative\">\n            {/* Background decoration */}\n            <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-3xl transform rotate-3\"></div>\n            \n            <div className=\"relative bg-white rounded-3xl p-8 shadow-xl\">\n              <h3 className=\"text-2xl font-bold text-gray-900 mb-8 text-center\">\n                Our Impact in Numbers\n              </h3>\n              \n              <div className=\"space-y-8\">\n                {stats.map((stat, index) => {\n                  const IconComponent = stat.icon;\n                  return (\n                    <div key={index} className=\"flex items-start space-x-4\">\n                      <div className=\"bg-blue-100 rounded-2xl p-3 flex-shrink-0\">\n                        <IconComponent className=\"w-8 h-8 text-blue-600\" />\n                      </div>\n                      <div>\n                        <div className=\"text-3xl font-bold text-gray-900 mb-1\">\n                          {stat.number}\n                        </div>\n                        <div className=\"text-lg font-semibold text-gray-800 mb-1\">\n                          {stat.label}\n                        </div>\n                        <div className=\"text-gray-600 text-sm\">\n                          {stat.description}\n                        </div>\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n\n              {/* Achievement badge */}\n              <div className=\"mt-8 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl text-center\">\n                <div className=\"text-sm font-semibold text-blue-600 mb-1\">\n                  🏆 Award Winning\n                </div>\n                <div className=\"text-xs text-gray-600\">\n                  Recognized as Nepal's leading education consultancy\n                </div>\n              </div>\n            </div>\n\n            {/* Floating elements */}\n            <div className=\"absolute -top-4 -right-4 w-20 h-20 bg-yellow-200 rounded-full mix-blend-multiply filter blur-xl opacity-70\"></div>\n            <div className=\"absolute -bottom-4 -left-4 w-20 h-20 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-70\"></div>\n          </div>\n        </div>\n\n        {/* Bottom section */}\n        <div className=\"mt-16 text-center\">\n          <div className=\"bg-gradient-to-r from-gray-50 to-blue-50 rounded-3xl p-8 max-w-4xl mx-auto\">\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n              Why Choose AECC Nepal?\n            </h3>\n            <p className=\"text-gray-600 mb-6 max-w-2xl mx-auto\">\n              We are committed to providing honest, transparent, and personalized guidance to help you achieve your study abroad dreams. Our success is measured by your success.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button className=\"bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors shadow-md\">\n                Start Your Journey\n              </button>\n              <button className=\"border border-gray-300 text-gray-700 px-8 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors\">\n                Learn More About Us\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default AboutSection;\n"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AAAA;;;AAEA,MAAM,eAAe;IACnB,MAAM,QAAQ;QACZ;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,QAAQ;YACR,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,QAAQ;YACR,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,QAAQ;YACR,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;sDAGlE,8OAAC;4CAAE,WAAU;sDAA6C;;;;;;sDAG1D,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;;;;;;;8CAMpD,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,8OAAC;oDAAK,WAAU;8DAAiB;;;;;;;2CAFzB;;;;;;;;;;8CAQd,8OAAC;oCAAO,WAAU;8CAA0G;;;;;;;;;;;;sCAM9H,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;sDAIlE,8OAAC;4CAAI,WAAU;sDACZ,MAAM,GAAG,CAAC,CAAC,MAAM;gDAChB,MAAM,gBAAgB,KAAK,IAAI;gDAC/B,qBACE,8OAAC;oDAAgB,WAAU;;sEACzB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAc,WAAU;;;;;;;;;;;sEAE3B,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EACZ,KAAK,MAAM;;;;;;8EAEd,8OAAC;oEAAI,WAAU;8EACZ,KAAK,KAAK;;;;;;8EAEb,8OAAC;oEAAI,WAAU;8EACZ,KAAK,WAAW;;;;;;;;;;;;;mDAZb;;;;;4CAiBd;;;;;;sDAIF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAA2C;;;;;;8DAG1D,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAO3C,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;8BAKnB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAGpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAA0G;;;;;;kDAG5H,8OAAC;wCAAO,WAAU;kDAA6G;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7I;uCAEe", "debugId": null}}, {"offset": {"line": 1788, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/consultancy/aecc-clone/src/components/sections/StudyDestinationsSection.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { ArrowR<PERSON>, MapPin } from 'lucide-react';\n\nconst StudyDestinationsSection = () => {\n  const destinations = [\n    {\n      name: 'Study in UK',\n      href: '/study-abroad/study-in-uk',\n      image: '/images/uk.webp',\n      description: 'World-class education with rich cultural heritage',\n      highlights: ['Top universities', 'Post-study work visa', 'Scholarships available'],\n      color: 'from-red-500 to-blue-600'\n    },\n    {\n      name: 'Study in Canada',\n      href: '/study-abroad/study-in-canada',\n      image: '/images/canada.webp',\n      description: 'Quality education with excellent immigration opportunities',\n      highlights: ['Affordable tuition', 'Work permits', 'Pathway to PR'],\n      color: 'from-red-600 to-red-800'\n    },\n    {\n      name: 'Study in Australia',\n      href: '/study-abroad/study-in-australia',\n      image: '/images/australia.webp',\n      description: 'Innovative education system with beautiful landscapes',\n      highlights: ['Research opportunities', 'Work while studying', 'High quality of life'],\n      color: 'from-green-500 to-blue-600'\n    },\n    {\n      name: 'Study in USA',\n      href: '/study-abroad/study-in-usa',\n      image: '/images/usa.webp',\n      description: 'Home to world\\'s top universities and innovation',\n      highlights: ['Ivy League universities', 'Research funding', 'Career opportunities'],\n      color: 'from-blue-600 to-red-600'\n    },\n    {\n      name: 'Study in New Zealand',\n      href: '/study-abroad/study-in-new-zealand',\n      image: '/images/new-zealand.webp',\n      description: 'Safe, peaceful country with excellent education',\n      highlights: ['Safe environment', 'Work opportunities', 'Beautiful nature'],\n      color: 'from-green-600 to-blue-500'\n    },\n    {\n      name: 'Study in Europe',\n      href: '/study-abroad/study-in-europe',\n      image: '/images/europe.png',\n      description: 'Diverse cultures and affordable education options',\n      highlights: ['Low tuition fees', 'Cultural diversity', 'Travel opportunities'],\n      color: 'from-purple-600 to-blue-600'\n    }\n  ];\n\n  return (\n    <section className=\"py-16 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            Wherever you want to go, we'll get you there.\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n            Explore the best study destinations in the world! Learn all about the countries' top universities, scholarships, cost of living, post-study work rights and more\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {destinations.map((destination, index) => (\n            <Link\n              key={destination.name}\n              href={destination.href}\n              className=\"group relative overflow-hidden rounded-2xl bg-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2\"\n            >\n              {/* Background Image/Gradient */}\n              <div className={`relative h-48 bg-gradient-to-br ${destination.color} overflow-hidden`}>\n                {/* Country flag or icon placeholder */}\n                <div className=\"absolute inset-0 flex items-center justify-center\">\n                  <div className=\"bg-white/20 backdrop-blur-sm rounded-full p-6\">\n                    <MapPin className=\"w-12 h-12 text-white\" />\n                  </div>\n                </div>\n\n                {/* Overlay */}\n                <div className=\"absolute inset-0 bg-black/20 group-hover:bg-black/30 transition-colors\"></div>\n                \n                {/* Country name overlay */}\n                <div className=\"absolute bottom-4 left-4 right-4\">\n                  <h3 className=\"text-xl font-bold text-white mb-1\">\n                    {destination.name}\n                  </h3>\n                </div>\n              </div>\n\n              {/* Content */}\n              <div className=\"p-6\">\n                <p className=\"text-gray-600 text-sm mb-4 leading-relaxed\">\n                  {destination.description}\n                </p>\n\n                {/* Highlights */}\n                <div className=\"space-y-2 mb-4\">\n                  {destination.highlights.map((highlight, idx) => (\n                    <div key={idx} className=\"flex items-center text-sm text-gray-600\">\n                      <div className=\"w-1.5 h-1.5 bg-blue-500 rounded-full mr-2 flex-shrink-0\"></div>\n                      {highlight}\n                    </div>\n                  ))}\n                </div>\n\n                {/* Learn More Link */}\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-blue-600 font-medium text-sm group-hover:text-blue-700 transition-colors\">\n                    Learn more\n                  </span>\n                  <ArrowRight className=\"w-4 h-4 text-blue-600 group-hover:translate-x-1 transition-transform\" />\n                </div>\n              </div>\n\n              {/* Decorative elements */}\n              <div className=\"absolute top-4 right-4 w-8 h-8 bg-white/20 rounded-full\"></div>\n              <div className=\"absolute top-6 right-6 w-4 h-4 bg-white/30 rounded-full\"></div>\n            </Link>\n          ))}\n        </div>\n\n        {/* Bottom CTA */}\n        <div className=\"text-center mt-12\">\n          <div className=\"bg-white rounded-2xl p-8 shadow-lg max-w-2xl mx-auto\">\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n              Not sure which destination is right for you?\n            </h3>\n            <p className=\"text-gray-600 mb-6\">\n              Our expert counselors can help you choose the perfect study destination based on your goals, budget, and preferences.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button className=\"bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors shadow-md\">\n                Get Personalized Advice\n              </button>\n              <button className=\"border border-blue-600 text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors\">\n                Compare Destinations\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default StudyDestinationsSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;;;;AAEA,MAAM,2BAA2B;IAC/B,MAAM,eAAe;QACnB;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,aAAa;YACb,YAAY;gBAAC;gBAAoB;gBAAwB;aAAyB;YAClF,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,aAAa;YACb,YAAY;gBAAC;gBAAsB;gBAAgB;aAAgB;YACnE,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,aAAa;YACb,YAAY;gBAAC;gBAA0B;gBAAuB;aAAuB;YACrF,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,aAAa;YACb,YAAY;gBAAC;gBAA2B;gBAAoB;aAAuB;YACnF,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,aAAa;YACb,YAAY;gBAAC;gBAAoB;gBAAsB;aAAmB;YAC1E,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,aAAa;YACb,YAAY;gBAAC;gBAAoB;gBAAsB;aAAuB;YAC9E,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,8OAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC,4JAAA,CAAA,UAAI;4BAEH,MAAM,YAAY,IAAI;4BACtB,WAAU;;8CAGV,8OAAC;oCAAI,WAAW,CAAC,gCAAgC,EAAE,YAAY,KAAK,CAAC,gBAAgB,CAAC;;sDAEpF,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAKtB,8OAAC;4CAAI,WAAU;;;;;;sDAGf,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;0DACX,YAAY,IAAI;;;;;;;;;;;;;;;;;8CAMvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDACV,YAAY,WAAW;;;;;;sDAI1B,8OAAC;4CAAI,WAAU;sDACZ,YAAY,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,oBACtC,8OAAC;oDAAc,WAAU;;sEACvB,8OAAC;4DAAI,WAAU;;;;;;wDACd;;mDAFO;;;;;;;;;;sDAQd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgF;;;;;;8DAGhG,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;8CAK1B,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;2BAnDV,YAAY,IAAI;;;;;;;;;;8BAyD3B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAA0G;;;;;;kDAG5H,8OAAC;wCAAO,WAAU;kDAA6G;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7I;uCAEe", "debugId": null}}, {"offset": {"line": 2125, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/consultancy/aecc-clone/src/components/sections/TestimonialsSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/TestimonialsSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/TestimonialsSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmT,GAChV,iFACA", "debugId": null}}, {"offset": {"line": 2137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/consultancy/aecc-clone/src/components/sections/TestimonialsSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/TestimonialsSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/TestimonialsSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "debugId": null}}, {"offset": {"line": 2149, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2157, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/consultancy/aecc-clone/src/components/sections/PartnershipsSection.tsx"], "sourcesContent": ["import React from 'react';\nimport { Award, Shield, Users, Globe } from 'lucide-react';\n\nconst PartnershipsSection = () => {\n  const partnerships = [\n    {\n      category: 'Education Partners',\n      description: 'Top universities worldwide',\n      icon: '🎓',\n      count: '500+'\n    },\n    {\n      category: 'Government Accreditations',\n      description: 'Official recognitions',\n      icon: '🏛️',\n      count: '15+'\n    },\n    {\n      category: 'Industry Associations',\n      description: 'Professional memberships',\n      icon: '🤝',\n      count: '25+'\n    },\n    {\n      category: 'Global Offices',\n      description: 'Countries we serve',\n      icon: '🌍',\n      count: '12+'\n    }\n  ];\n\n  const accreditations = [\n    {\n      name: 'PIER',\n      description: 'Professional International Education Resources',\n      logo: '🏆'\n    },\n    {\n      name: 'ICEF',\n      description: 'International Consultants for Education and Fairs',\n      logo: '🌟'\n    },\n    {\n      name: 'British Council',\n      description: 'Official UK Education Partner',\n      logo: '🇬🇧'\n    },\n    {\n      name: 'IDP Education',\n      description: 'Global Education Services',\n      logo: '📚'\n    },\n    {\n      name: 'Education USA',\n      description: 'US Department of State Network',\n      logo: '🇺🇸'\n    },\n    {\n      name: 'AIRC',\n      description: 'American International Recruitment Council',\n      logo: '⭐'\n    }\n  ];\n\n  return (\n    <section className=\"py-16 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            Our industry partnerships\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n            Our affiliations with industry leaders, accreditations, and partnerships speak volumes about our credibility and standing.\n          </p>\n        </div>\n\n        {/* Partnership Stats */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16\">\n          {partnerships.map((partnership, index) => (\n            <div key={index} className=\"bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-shadow\">\n              <div className=\"text-4xl mb-4\">{partnership.icon}</div>\n              <div className=\"text-3xl font-bold text-blue-600 mb-2\">\n                {partnership.count}\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                {partnership.category}\n              </h3>\n              <p className=\"text-gray-600 text-sm\">\n                {partnership.description}\n              </p>\n            </div>\n          ))}\n        </div>\n\n        {/* Accreditations Grid */}\n        <div className=\"bg-white rounded-3xl p-8 shadow-lg\">\n          <h3 className=\"text-2xl font-bold text-gray-900 text-center mb-8\">\n            Accreditations & Memberships\n          </h3>\n          \n          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6\">\n            {accreditations.map((accreditation, index) => (\n              <div key={index} className=\"text-center group\">\n                <div className=\"bg-gray-50 rounded-2xl p-6 mb-3 group-hover:bg-blue-50 transition-colors\">\n                  <div className=\"text-3xl mb-2\">{accreditation.logo}</div>\n                  <h4 className=\"font-semibold text-gray-900 text-sm mb-1\">\n                    {accreditation.name}\n                  </h4>\n                  <p className=\"text-xs text-gray-600\">\n                    {accreditation.description}\n                  </p>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Trust Indicators */}\n        <div className=\"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8\">\n          <div className=\"text-center\">\n            <div className=\"bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\">\n              <Shield className=\"w-8 h-8 text-blue-600\" />\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n              Trusted & Verified\n            </h3>\n            <p className=\"text-gray-600 text-sm\">\n              All our partnerships are verified and we maintain the highest standards of service.\n            </p>\n          </div>\n\n          <div className=\"text-center\">\n            <div className=\"bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\">\n              <Award className=\"w-8 h-8 text-green-600\" />\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n              Award Winning\n            </h3>\n            <p className=\"text-gray-600 text-sm\">\n              Recognized by industry leaders for excellence in international education consulting.\n            </p>\n          </div>\n\n          <div className=\"text-center\">\n            <div className=\"bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\">\n              <Globe className=\"w-8 h-8 text-purple-600\" />\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n              Global Network\n            </h3>\n            <p className=\"text-gray-600 text-sm\">\n              Connected with institutions and partners across multiple countries worldwide.\n            </p>\n          </div>\n        </div>\n\n        {/* Partner CTA */}\n        <div className=\"mt-16 text-center\">\n          <div className=\"bg-gradient-to-r from-blue-600 to-indigo-600 rounded-3xl p-8 text-white\">\n            <h3 className=\"text-2xl font-bold mb-4\">\n              Interested in partnering with us?\n            </h3>\n            <p className=\"text-blue-100 mb-6 max-w-2xl mx-auto\">\n              Join our network of trusted partners and help students achieve their study abroad dreams.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button className=\"bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors\">\n                Partner With Us\n              </button>\n              <button className=\"border border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white/10 transition-colors\">\n                View All Partners\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default PartnershipsSection;\n"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;;;AAEA,MAAM,sBAAsB;IAC1B,MAAM,eAAe;QACnB;YACE,UAAU;YACV,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,UAAU;YACV,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,UAAU;YACV,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,UAAU;YACV,aAAa;YACb,MAAM;YACN,OAAO;QACT;KACD;IAED,MAAM,iBAAiB;QACrB;YACE,MAAM;YACN,aAAa;YACb,MAAM;QACR;QACA;YACE,MAAM;YACN,aAAa;YACb,MAAM;QACR;QACA;YACE,MAAM;YACN,aAAa;YACb,MAAM;QACR;QACA;YACE,MAAM;YACN,aAAa;YACb,MAAM;QACR;QACA;YACE,MAAM;YACN,aAAa;YAC<PERSON>,MAAM;QACR;QACA;YACE,MAAM;YACN,aAAa;YACb,MAAM;QACR;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAI,WAAU;8CAAiB,YAAY,IAAI;;;;;;8CAChD,8OAAC;oCAAI,WAAU;8CACZ,YAAY,KAAK;;;;;;8CAEpB,8OAAC;oCAAG,WAAU;8CACX,YAAY,QAAQ;;;;;;8CAEvB,8OAAC;oCAAE,WAAU;8CACV,YAAY,WAAW;;;;;;;2BATlB;;;;;;;;;;8BAgBd,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAIlE,8OAAC;4BAAI,WAAU;sCACZ,eAAe,GAAG,CAAC,CAAC,eAAe,sBAClC,8OAAC;oCAAgB,WAAU;8CACzB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAiB,cAAc,IAAI;;;;;;0DAClD,8OAAC;gDAAG,WAAU;0DACX,cAAc,IAAI;;;;;;0DAErB,8OAAC;gDAAE,WAAU;0DACV,cAAc,WAAW;;;;;;;;;;;;mCAPtB;;;;;;;;;;;;;;;;8BAgBhB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAGzD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAGzD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAGzD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;8BAOzC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CAGxC,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAGpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAAgG;;;;;;kDAGlH,8OAAC;wCAAO,WAAU;kDAAwG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxI;uCAEe", "debugId": null}}, {"offset": {"line": 2567, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/consultancy/aecc-clone/src/components/sections/ContactFormSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/ContactFormSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/ContactFormSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkT,GAC/U,gFACA", "debugId": null}}, {"offset": {"line": 2579, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/consultancy/aecc-clone/src/components/sections/ContactFormSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/ContactFormSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/ContactFormSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8R,GAC3T,4DACA", "debugId": null}}, {"offset": {"line": 2591, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2599, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/consultancy/aecc-clone/src/app/page.tsx"], "sourcesContent": ["import Header from '@/components/layout/Header';\nimport Footer from '@/components/layout/Footer';\nimport HeroSection from '@/components/sections/HeroSection';\nimport EventsSection from '@/components/sections/EventsSection';\nimport ServicesSection from '@/components/sections/ServicesSection';\nimport AboutSection from '@/components/sections/AboutSection';\nimport StudyDestinationsSection from '@/components/sections/StudyDestinationsSection';\nimport TestimonialsSection from '@/components/sections/TestimonialsSection';\nimport PartnershipsSection from '@/components/sections/PartnershipsSection';\nimport ContactFormSection from '@/components/sections/ContactFormSection';\nimport BackToTop from '@/components/ui/BackToTop';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen flex flex-col\">\n      <Header />\n      <main className=\"flex-1\">\n        <HeroSection />\n        <EventsSection />\n        <ServicesSection />\n        <AboutSection />\n        <StudyDestinationsSection />\n        <TestimonialsSection />\n        <PartnershipsSection />\n        <ContactFormSection />\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AAGe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAK,WAAU;;kCACd,8OAAC,6IAAA,CAAA,UAAW;;;;;kCACZ,8OAAC,+IAAA,CAAA,UAAa;;;;;kCACd,8OAAC,iJAAA,CAAA,UAAe;;;;;kCAChB,8OAAC,8IAAA,CAAA,UAAY;;;;;kCACb,8OAAC,0JAAA,CAAA,UAAwB;;;;;kCACzB,8OAAC,qJAAA,CAAA,UAAmB;;;;;kCACpB,8OAAC,qJAAA,CAAA,UAAmB;;;;;kCACpB,8OAAC,oJAAA,CAAA,UAAkB;;;;;;;;;;;0BAErB,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}]}