import React from 'react';
import { Award, Shield, Users, Globe } from 'lucide-react';

const PartnershipsSection = () => {
  const partnerships = [
    {
      category: 'Education Partners',
      description: 'Top universities worldwide',
      icon: '🎓',
      count: '500+'
    },
    {
      category: 'Government Accreditations',
      description: 'Official recognitions',
      icon: '🏛️',
      count: '15+'
    },
    {
      category: 'Industry Associations',
      description: 'Professional memberships',
      icon: '🤝',
      count: '25+'
    },
    {
      category: 'Global Offices',
      description: 'Countries we serve',
      icon: '🌍',
      count: '12+'
    }
  ];

  const accreditations = [
    {
      name: 'PIER',
      description: 'Professional International Education Resources',
      logo: '🏆'
    },
    {
      name: 'ICEF',
      description: 'International Consultants for Education and Fairs',
      logo: '🌟'
    },
    {
      name: 'British Council',
      description: 'Official UK Education Partner',
      logo: '🇬🇧'
    },
    {
      name: 'IDP Education',
      description: 'Global Education Services',
      logo: '📚'
    },
    {
      name: 'Education USA',
      description: 'US Department of State Network',
      logo: '🇺🇸'
    },
    {
      name: 'AIRC',
      description: 'American International Recruitment Council',
      logo: '⭐'
    }
  ];

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Our industry partnerships
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Our affiliations with industry leaders, accreditations, and partnerships speak volumes about our credibility and standing.
          </p>
        </div>

        {/* Partnership Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {partnerships.map((partnership, index) => (
            <div key={index} className="bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-shadow">
              <div className="text-4xl mb-4">{partnership.icon}</div>
              <div className="text-3xl font-bold text-blue-600 mb-2">
                {partnership.count}
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {partnership.category}
              </h3>
              <p className="text-gray-600 text-sm">
                {partnership.description}
              </p>
            </div>
          ))}
        </div>

        {/* Accreditations Grid */}
        <div className="bg-white rounded-3xl p-8 shadow-lg">
          <h3 className="text-2xl font-bold text-gray-900 text-center mb-8">
            Accreditations & Memberships
          </h3>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {accreditations.map((accreditation, index) => (
              <div key={index} className="text-center group">
                <div className="bg-gray-50 rounded-2xl p-6 mb-3 group-hover:bg-blue-50 transition-colors">
                  <div className="text-3xl mb-2">{accreditation.logo}</div>
                  <h4 className="font-semibold text-gray-900 text-sm mb-1">
                    {accreditation.name}
                  </h4>
                  <p className="text-xs text-gray-600">
                    {accreditation.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Trust Indicators */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <Shield className="w-8 h-8 text-blue-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Trusted & Verified
            </h3>
            <p className="text-gray-600 text-sm">
              All our partnerships are verified and we maintain the highest standards of service.
            </p>
          </div>

          <div className="text-center">
            <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <Award className="w-8 h-8 text-green-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Award Winning
            </h3>
            <p className="text-gray-600 text-sm">
              Recognized by industry leaders for excellence in international education consulting.
            </p>
          </div>

          <div className="text-center">
            <div className="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <Globe className="w-8 h-8 text-purple-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Global Network
            </h3>
            <p className="text-gray-600 text-sm">
              Connected with institutions and partners across multiple countries worldwide.
            </p>
          </div>
        </div>

        {/* Partner CTA */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-3xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">
              Interested in partnering with us?
            </h3>
            <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
              Join our network of trusted partners and help students achieve their study abroad dreams.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                Partner With Us
              </button>
              <button className="border border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white/10 transition-colors">
                View All Partners
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PartnershipsSection;
