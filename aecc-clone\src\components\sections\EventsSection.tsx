import React from 'react';
import Link from 'next/link';
import { MapPin, Calendar } from 'lucide-react';

const EventsSection = () => {
  const cities = [
    {
      name: 'Kathmandu',
      href: '/upcoming-event/kathmandu',
      image: '/images/kathmandu-bg.jpg',
      description: 'Capital city events and seminars'
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      href: '/upcoming-event/chitwan',
      image: '/images/chitwan-bg.jpg',
      description: 'Regional consultation sessions'
    },
    {
      name: 'Pokhara',
      href: '/upcoming-event/pokhara',
      image: '/images/pokhara-bg.jpg',
      description: 'Study abroad information sessions'
    }
  ];

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Find events near you
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            From your hometown to the world: Find study abroad events in your city today
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          {cities.map((city, index) => (
            <Link
              key={city.name}
              href={city.href}
              className="group relative overflow-hidden rounded-2xl bg-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2"
            >
              {/* City Image Background */}
              <div className="relative h-48 bg-gradient-to-br from-blue-400 to-indigo-600 overflow-hidden">
                {/* Placeholder gradient background */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-400 to-indigo-600"></div>
                
                {/* City Icon */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="bg-white/20 backdrop-blur-sm rounded-full p-6">
                    <MapPin className="w-12 h-12 text-white" />
                  </div>
                </div>

                {/* Overlay */}
                <div className="absolute inset-0 bg-black/20 group-hover:bg-black/30 transition-colors"></div>
              </div>

              {/* Content */}
              <div className="p-6">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
                    {city.name}
                  </h3>
                  <Calendar className="w-5 h-5 text-gray-400" />
                </div>
                <p className="text-gray-600 text-sm mb-4">
                  {city.description}
                </p>
                <div className="flex items-center text-blue-600 font-medium text-sm">
                  <span>View Events</span>
                  <svg className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>

              {/* Decorative element */}
              <div className="absolute top-4 right-4 w-8 h-8 bg-white/20 rounded-full"></div>
              <div className="absolute top-6 right-6 w-4 h-4 bg-white/30 rounded-full"></div>
            </Link>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center mt-12">
          <div className="bg-white rounded-2xl p-8 shadow-lg max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Can't find an event in your city?
            </h3>
            <p className="text-gray-600 mb-6">
              Book a free online consultation with our expert counselors
            </p>
            <button className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors shadow-md">
              Book Free Consultation
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default EventsSection;
