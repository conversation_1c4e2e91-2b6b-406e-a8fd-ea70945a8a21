import React from 'react';
import Link from 'next/link';
import { ArrowR<PERSON>, MapPin } from 'lucide-react';

const StudyDestinationsSection = () => {
  const destinations = [
    {
      name: 'Study in UK',
      href: '/study-abroad/study-in-uk',
      image: '/images/uk.webp',
      description: 'World-class education with rich cultural heritage',
      highlights: ['Top universities', 'Post-study work visa', 'Scholarships available'],
      color: 'from-red-500 to-blue-600'
    },
    {
      name: 'Study in Canada',
      href: '/study-abroad/study-in-canada',
      image: '/images/canada.webp',
      description: 'Quality education with excellent immigration opportunities',
      highlights: ['Affordable tuition', 'Work permits', 'Pathway to PR'],
      color: 'from-red-600 to-red-800'
    },
    {
      name: 'Study in Australia',
      href: '/study-abroad/study-in-australia',
      image: '/images/australia.webp',
      description: 'Innovative education system with beautiful landscapes',
      highlights: ['Research opportunities', 'Work while studying', 'High quality of life'],
      color: 'from-green-500 to-blue-600'
    },
    {
      name: 'Study in USA',
      href: '/study-abroad/study-in-usa',
      image: '/images/usa.webp',
      description: 'Home to world\'s top universities and innovation',
      highlights: ['Ivy League universities', 'Research funding', 'Career opportunities'],
      color: 'from-blue-600 to-red-600'
    },
    {
      name: 'Study in New Zealand',
      href: '/study-abroad/study-in-new-zealand',
      image: '/images/new-zealand.webp',
      description: 'Safe, peaceful country with excellent education',
      highlights: ['Safe environment', 'Work opportunities', 'Beautiful nature'],
      color: 'from-green-600 to-blue-500'
    },
    {
      name: 'Study in Europe',
      href: '/study-abroad/study-in-europe',
      image: '/images/europe.png',
      description: 'Diverse cultures and affordable education options',
      highlights: ['Low tuition fees', 'Cultural diversity', 'Travel opportunities'],
      color: 'from-purple-600 to-blue-600'
    }
  ];

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Wherever you want to go, we'll get you there.
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Explore the best study destinations in the world! Learn all about the countries' top universities, scholarships, cost of living, post-study work rights and more
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {destinations.map((destination, index) => (
            <Link
              key={destination.name}
              href={destination.href}
              className="group relative overflow-hidden rounded-2xl bg-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2"
            >
              {/* Background Image/Gradient */}
              <div className={`relative h-48 bg-gradient-to-br ${destination.color} overflow-hidden`}>
                {/* Country flag or icon placeholder */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="bg-white/20 backdrop-blur-sm rounded-full p-6">
                    <MapPin className="w-12 h-12 text-white" />
                  </div>
                </div>

                {/* Overlay */}
                <div className="absolute inset-0 bg-black/20 group-hover:bg-black/30 transition-colors"></div>
                
                {/* Country name overlay */}
                <div className="absolute bottom-4 left-4 right-4">
                  <h3 className="text-xl font-bold text-white mb-1">
                    {destination.name}
                  </h3>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                  {destination.description}
                </p>

                {/* Highlights */}
                <div className="space-y-2 mb-4">
                  {destination.highlights.map((highlight, idx) => (
                    <div key={idx} className="flex items-center text-sm text-gray-600">
                      <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2 flex-shrink-0"></div>
                      {highlight}
                    </div>
                  ))}
                </div>

                {/* Learn More Link */}
                <div className="flex items-center justify-between">
                  <span className="text-blue-600 font-medium text-sm group-hover:text-blue-700 transition-colors">
                    Learn more
                  </span>
                  <ArrowRight className="w-4 h-4 text-blue-600 group-hover:translate-x-1 transition-transform" />
                </div>
              </div>

              {/* Decorative elements */}
              <div className="absolute top-4 right-4 w-8 h-8 bg-white/20 rounded-full"></div>
              <div className="absolute top-6 right-6 w-4 h-4 bg-white/30 rounded-full"></div>
            </Link>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-12">
          <div className="bg-white rounded-2xl p-8 shadow-lg max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Not sure which destination is right for you?
            </h3>
            <p className="text-gray-600 mb-6">
              Our expert counselors can help you choose the perfect study destination based on your goals, budget, and preferences.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors shadow-md">
                Get Personalized Advice
              </button>
              <button className="border border-blue-600 text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors">
                Compare Destinations
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default StudyDestinationsSection;
